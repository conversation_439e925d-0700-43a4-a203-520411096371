#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de construction de l'exécutable pour Bar-Resto Manager
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def check_dependencies():
    """Vérifie que toutes les dépendances sont installées"""
    print("🔍 Vérification des dépendances...")
    
    required_packages = [
        'PyQt5', 'SQLAlchemy', 'bcrypt', 'pyinstaller'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.lower().replace('-', '_'))
            print(f"✓ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} manquant")
    
    if missing_packages:
        print(f"\n⚠️  Packages manquants: {', '.join(missing_packages)}")
        print("Installez-les avec: pip install " + " ".join(missing_packages))
        return False
    
    print("✅ Toutes les dépendances sont installées !")
    return True

def clean_build_dirs():
    """Nettoie les répertoires de build précédents"""
    print("\n🧹 Nettoyage des builds précédents...")
    
    dirs_to_clean = ['build', 'dist', '__pycache__']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✓ Supprimé: {dir_name}")
    
    # Nettoyer les fichiers .spec
    for spec_file in Path('.').glob('*.spec'):
        spec_file.unlink()
        print(f"✓ Supprimé: {spec_file}")

def create_spec_file():
    """Crée un fichier .spec personnalisé pour PyInstaller"""
    print("\n📝 Création du fichier de configuration...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('src', 'src'),
        ('data', 'data'),
        ('resources', 'resources'),
    ],
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui', 
        'PyQt5.QtWidgets',
        'SQLAlchemy',
        'bcrypt',
        'sqlite3',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='BarRestoManager',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''
    
    with open('bar_resto.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✓ Fichier bar_resto.spec créé")

def build_executable():
    """Construit l'exécutable avec PyInstaller"""
    print("\n🔨 Construction de l'exécutable...")
    print("Cela peut prendre plusieurs minutes...")
    
    try:
        # Utiliser le fichier .spec personnalisé
        result = subprocess.run([
            sys.executable, '-m', 'PyInstaller',
            '--clean',
            'bar_resto.spec'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Construction réussie !")
            return True
        else:
            print("❌ Erreur lors de la construction:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def create_installer_files():
    """Crée les fichiers d'installation"""
    print("\n📦 Création des fichiers d'installation...")
    
    # Créer un README pour l'utilisateur final
    readme_content = """# Bar-Resto Manager

## Installation

1. Copiez le dossier complet dans le répertoire de votre choix
2. Lancez BarRestoManager.exe
3. Connectez-vous avec les identifiants par défaut :
   - Utilisateur: admin
   - Mot de passe: admin123

## Première utilisation

1. Changez le mot de passe administrateur
2. Créez vos utilisateurs (gérants, serveurs)
3. Ajoutez vos produits
4. Configurez les seuils d'alerte de stock

## Support

Pour toute question ou problème, contactez le support technique.

## Sauvegarde

La base de données se trouve dans le dossier 'data'.
Sauvegardez régulièrement ce dossier pour protéger vos données.
"""
    
    dist_path = Path('dist')
    if dist_path.exists():
        readme_path = dist_path / 'README.txt'
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        print("✓ README.txt créé")
        
        # Copier les dossiers nécessaires
        for folder in ['data', 'reports', 'resources']:
            src_folder = Path(folder)
            if src_folder.exists():
                dest_folder = dist_path / folder
                if dest_folder.exists():
                    shutil.rmtree(dest_folder)
                shutil.copytree(src_folder, dest_folder)
                print(f"✓ Dossier {folder} copié")

def main():
    """Fonction principale"""
    print("🏗️  Construction de l'exécutable Bar-Resto Manager")
    print("=" * 60)
    
    # Vérifier les dépendances
    if not check_dependencies():
        return 1
    
    # Nettoyer les builds précédents
    clean_build_dirs()
    
    # Créer le fichier .spec
    create_spec_file()
    
    # Construire l'exécutable
    if not build_executable():
        return 1
    
    # Créer les fichiers d'installation
    create_installer_files()
    
    print("\n" + "=" * 60)
    print("🎉 CONSTRUCTION TERMINÉE AVEC SUCCÈS !")
    print("\n📁 L'exécutable se trouve dans le dossier 'dist'")
    print("📋 Fichiers créés:")
    
    dist_path = Path('dist')
    if dist_path.exists():
        for item in dist_path.iterdir():
            print(f"   • {item.name}")
    
    print("\n🚀 Vous pouvez maintenant distribuer l'application !")
    print("💡 Testez l'exécutable avant la distribution finale.")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
