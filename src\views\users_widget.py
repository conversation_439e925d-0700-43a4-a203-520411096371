#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Widget de gestion des utilisateurs - Bar-Resto Manager
"""

from datetime import datetime
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QPushButton, QTableWidget, QTableWidgetItem,
                            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox,
                            QTextEdit, QDialog, QDialogButtonBox, QMessageBox,
                            QHeaderView, QAbstractItemView, QGroupBox, QFormLayout,
                            QCheckBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QIcon

from models.database import DatabaseManager
from models.services import UserService
from utils.auth import SessionManager, get_current_user
from utils.config import UserRole, PERMISSIONS

class UserDialog(QDialog):
    """Dialog pour ajouter/modifier un utilisateur"""

    def __init__(self, user=None, parent=None):
        super().__init__(parent)
        self.user = user
        self.user_service = UserService(DatabaseManager())
        self.init_ui()

        if user:
            self.load_user_data()

    def init_ui(self):
        """Initialise l'interface du dialog"""
        self.setWindowTitle("Nouvel Utilisateur" if not self.user else "Modifier Utilisateur")
        self.setFixedSize(400, 350)

        layout = QVBoxLayout(self)

        # Formulaire
        form_layout = QFormLayout()

        # Nom d'utilisateur
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("Nom d'utilisateur unique")
        form_layout.addRow("Nom d'utilisateur:", self.username_input)

        # Nom complet
        self.fullname_input = QLineEdit()
        self.fullname_input.setPlaceholderText("Nom complet")
        form_layout.addRow("Nom complet:", self.fullname_input)

        # Rôle
        self.role_combo = QComboBox()
        self.role_combo.addItems(["Admin", "Gérant", "Serveur"])
        form_layout.addRow("Rôle:", self.role_combo)

        # Mot de passe (seulement pour nouveaux utilisateurs)
        if not self.user:
            self.password_input = QLineEdit()
            self.password_input.setEchoMode(QLineEdit.Password)
            self.password_input.setPlaceholderText("Mot de passe")
            form_layout.addRow("Mot de passe:", self.password_input)

            self.confirm_password_input = QLineEdit()
            self.confirm_password_input.setEchoMode(QLineEdit.Password)
            self.confirm_password_input.setPlaceholderText("Confirmer le mot de passe")
            form_layout.addRow("Confirmer:", self.confirm_password_input)

        # Utilisateur actif
        self.active_checkbox = QCheckBox("Utilisateur actif")
        self.active_checkbox.setChecked(True)
        form_layout.addRow("", self.active_checkbox)

        layout.addLayout(form_layout)

        # Informations sur les permissions
        if self.role_combo.currentText():
            self.create_permissions_info(layout)

        self.role_combo.currentTextChanged.connect(self.update_permissions_info)

        # Boutons
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        # Style
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QLineEdit, QComboBox {
                padding: 8px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background-color: white;
            }
            QLineEdit:focus, QComboBox:focus {
                border-color: #007bff;
            }
        """)

    def create_permissions_info(self, layout):
        """Crée la section d'information sur les permissions"""
        self.permissions_group = QGroupBox("Permissions du Rôle")
        permissions_layout = QVBoxLayout(self.permissions_group)

        self.permissions_label = QLabel()
        self.permissions_label.setWordWrap(True)
        self.permissions_label.setObjectName("permissions_info")
        permissions_layout.addWidget(self.permissions_label)

        layout.addWidget(self.permissions_group)
        self.update_permissions_info()

    def update_permissions_info(self):
        """Met à jour l'affichage des permissions"""
        if not hasattr(self, 'permissions_label'):
            return

        role_text = self.role_combo.currentText().lower()
        role_map = {"admin": UserRole.ADMIN, "gérant": UserRole.GERANT, "serveur": UserRole.SERVEUR}

        if role_text in role_map:
            role = role_map[role_text]
            permissions = PERMISSIONS.get(role, {})

            allowed_permissions = [perm for perm, allowed in permissions.items() if allowed]

            permissions_text = "Permissions accordées:\n"
            permission_names = {
                'login': '• Se connecter',
                'manage_users': '• Gérer les utilisateurs',
                'manage_products': '• Gérer les produits',
                'make_sales': '• Effectuer des ventes',
                'view_sales_history': '• Voir l\'historique des ventes',
                'manage_stock': '• Gérer le stock',
                'view_stock': '• Voir les stocks',
                'generate_reports': '• Générer des rapports',
                'manage_expenses': '• Gérer les dépenses',
                'delete_operations': '• Supprimer des opérations',
                'manage_database': '• Gérer la base de données'
            }

            for perm in allowed_permissions:
                if perm in permission_names:
                    permissions_text += permission_names[perm] + "\n"

            self.permissions_label.setText(permissions_text)

    def load_user_data(self):
        """Charge les données de l'utilisateur à modifier"""
        if self.user:
            self.username_input.setText(self.user.username)
            self.fullname_input.setText(self.user.full_name)

            # Sélectionner le rôle
            role_map = {"admin": "Admin", "gerant": "Gérant", "serveur": "Serveur"}
            role_text = role_map.get(self.user.role, "Serveur")
            role_index = self.role_combo.findText(role_text)
            if role_index >= 0:
                self.role_combo.setCurrentIndex(role_index)

            self.active_checkbox.setChecked(self.user.is_active)

    def accept(self):
        """Valide et sauvegarde l'utilisateur"""
        username = self.username_input.text().strip()
        fullname = self.fullname_input.text().strip()

        # Validation
        if not username:
            QMessageBox.warning(self, "Erreur", "Le nom d'utilisateur est requis.")
            return

        if not fullname:
            QMessageBox.warning(self, "Erreur", "Le nom complet est requis.")
            return

        # Validation du mot de passe pour nouveaux utilisateurs
        if not self.user:
            password = self.password_input.text()
            confirm_password = self.confirm_password_input.text()

            if not password:
                QMessageBox.warning(self, "Erreur", "Le mot de passe est requis.")
                return

            if len(password) < 6:
                QMessageBox.warning(self, "Erreur", "Le mot de passe doit contenir au moins 6 caractères.")
                return

            if password != confirm_password:
                QMessageBox.warning(self, "Erreur", "Les mots de passe ne correspondent pas.")
                return

        try:
            # Mapper le rôle
            role_map = {"Admin": "admin", "Gérant": "gerant", "Serveur": "serveur"}
            role = role_map[self.role_combo.currentText()]

            if self.user:
                # Modification (à implémenter dans le service)
                QMessageBox.information(self, "Info", "Modification d'utilisateurs non implémentée.")
                return
            else:
                # Création
                self.user = self.user_service.create_user(
                    username=username,
                    password=self.password_input.text(),
                    full_name=fullname,
                    role=role
                )

            super().accept()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la sauvegarde: {e}")

class UsersWidget(QWidget):
    """Widget pour la gestion des utilisateurs"""

    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.user_service = UserService(self.db_manager)
        self.session_manager = SessionManager()

        self.init_ui()
        self.setup_styles()
        self.refresh_data()

    def init_ui(self):
        """Initialise l'interface utilisateur"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)

        # Titre
        title_label = QLabel("👥 Gestion des Utilisateurs")
        title_label.setObjectName("page_title")
        layout.addWidget(title_label)

        # Barre d'outils
        self.create_toolbar(layout)

        # Tableau des utilisateurs
        self.create_users_table(layout)

        # Informations sur les rôles
        self.create_roles_info(layout)

    def create_toolbar(self, layout):
        """Crée la barre d'outils"""
        toolbar_layout = QHBoxLayout()

        # Bouton Nouvel utilisateur
        self.new_button = QPushButton("➕ Nouvel Utilisateur")
        self.new_button.setObjectName("primary_button")
        self.new_button.clicked.connect(self.add_user)
        toolbar_layout.addWidget(self.new_button)

        # Bouton Modifier
        self.edit_button = QPushButton("✏️ Modifier")
        self.edit_button.setObjectName("secondary_button")
        self.edit_button.clicked.connect(self.edit_user)
        self.edit_button.setEnabled(False)
        toolbar_layout.addWidget(self.edit_button)

        # Bouton Désactiver/Activer
        self.toggle_button = QPushButton("🔒 Désactiver")
        self.toggle_button.setObjectName("warning_button")
        self.toggle_button.clicked.connect(self.toggle_user_status)
        self.toggle_button.setEnabled(False)
        toolbar_layout.addWidget(self.toggle_button)

        toolbar_layout.addStretch()

        # Bouton Actualiser
        refresh_button = QPushButton("🔄 Actualiser")
        refresh_button.setObjectName("secondary_button")
        refresh_button.clicked.connect(self.refresh_data)
        toolbar_layout.addWidget(refresh_button)

        layout.addLayout(toolbar_layout)

    def create_users_table(self, layout):
        """Crée le tableau des utilisateurs"""
        self.users_table = QTableWidget()
        self.users_table.setColumnCount(6)
        self.users_table.setHorizontalHeaderLabels([
            "Nom d'utilisateur", "Nom complet", "Rôle", "Dernière connexion", "Statut", "Créé le"
        ])

        # Configuration du tableau
        header = self.users_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # Nom complet

        self.users_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.users_table.setAlternatingRowColors(True)
        self.users_table.setSortingEnabled(True)

        # Connexion des signaux
        self.users_table.selectionModel().selectionChanged.connect(self.on_selection_changed)
        self.users_table.doubleClicked.connect(self.edit_user)

        layout.addWidget(self.users_table)

    def create_roles_info(self, layout):
        """Crée la section d'information sur les rôles"""
        roles_group = QGroupBox("Information sur les Rôles")
        roles_layout = QVBoxLayout(roles_group)

        roles_info = """
        <b>Admin:</b> Accès complet à toutes les fonctionnalités<br>
        <b>Gérant:</b> Gestion des produits, stocks, ventes et rapports<br>
        <b>Serveur:</b> Accès limité aux ventes uniquement
        """

        info_label = QLabel(roles_info)
        info_label.setWordWrap(True)
        info_label.setObjectName("roles_info")
        roles_layout.addWidget(info_label)

        layout.addWidget(roles_group)

    def setup_styles(self):
        """Configure les styles CSS"""
        style = """
        #page_title {
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        #primary_button {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: bold;
        }

        #primary_button:hover {
            background-color: #218838;
        }

        #secondary_button {
            background-color: #6c757d;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
        }

        #secondary_button:hover {
            background-color: #545b62;
        }

        #warning_button {
            background-color: #ffc107;
            color: #212529;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: bold;
        }

        #warning_button:hover {
            background-color: #e0a800;
        }

        #permissions_info {
            background-color: #e7f3ff;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }

        #roles_info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            color: #495057;
        }

        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 5px;
            margin-top: 10px;
            padding-top: 10px;
        }

        QTableWidget {
            gridline-color: #dee2e6;
            background-color: white;
            alternate-background-color: #f8f9fa;
        }

        QTableWidget::item {
            padding: 8px;
        }

        QHeaderView::section {
            background-color: #e9ecef;
            padding: 8px;
            border: none;
            font-weight: bold;
        }
        """
        self.setStyleSheet(style)

    def refresh_data(self):
        """Actualise les données du tableau"""
        try:
            # Récupérer tous les utilisateurs
            self.users = self.user_service.get_all_users()
            self.populate_table()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des données: {e}")

    def populate_table(self):
        """Remplit le tableau avec les utilisateurs"""
        self.users_table.setRowCount(len(self.users))

        for row, user in enumerate(self.users):
            # Nom d'utilisateur
            username_item = QTableWidgetItem(user.username)
            username_item.setData(Qt.UserRole, user)
            self.users_table.setItem(row, 0, username_item)

            # Nom complet
            self.users_table.setItem(row, 1, QTableWidgetItem(user.full_name))

            # Rôle
            role_display = {"admin": "Admin", "gerant": "Gérant", "serveur": "Serveur"}
            role_text = role_display.get(user.role, user.role)
            role_item = QTableWidgetItem(role_text)

            # Couleur selon le rôle
            if user.role == "admin":
                role_item.setForeground(Qt.red)
            elif user.role == "gerant":
                role_item.setForeground(Qt.blue)
            else:
                role_item.setForeground(Qt.darkGreen)

            self.users_table.setItem(row, 2, role_item)

            # Dernière connexion
            if user.last_login:
                last_login = user.last_login.strftime("%d/%m/%Y %H:%M")
            else:
                last_login = "Jamais"
            self.users_table.setItem(row, 3, QTableWidgetItem(last_login))

            # Statut
            status_item = QTableWidgetItem("Actif" if user.is_active else "Inactif")
            if user.is_active:
                status_item.setForeground(Qt.darkGreen)
            else:
                status_item.setForeground(Qt.red)
            status_item.setTextAlignment(Qt.AlignCenter)
            self.users_table.setItem(row, 4, status_item)

            # Date de création
            created_date = user.created_at.strftime("%d/%m/%Y")
            self.users_table.setItem(row, 5, QTableWidgetItem(created_date))

    def on_selection_changed(self):
        """Gère le changement de sélection dans le tableau"""
        has_selection = len(self.users_table.selectedItems()) > 0
        selected_user = self.get_selected_user()

        self.edit_button.setEnabled(has_selection)
        self.toggle_button.setEnabled(has_selection)

        # Mettre à jour le texte du bouton toggle
        if selected_user:
            if selected_user.is_active:
                self.toggle_button.setText("🔒 Désactiver")
            else:
                self.toggle_button.setText("🔓 Activer")

    def get_selected_user(self):
        """Retourne l'utilisateur sélectionné"""
        current_row = self.users_table.currentRow()
        if current_row >= 0:
            item = self.users_table.item(current_row, 0)
            if item:
                return item.data(Qt.UserRole)
        return None

    def add_user(self):
        """Ajoute un nouvel utilisateur"""
        dialog = UserDialog(parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.refresh_data()
            QMessageBox.information(self, "Succès", "Utilisateur créé avec succès!")

    def edit_user(self):
        """Modifie l'utilisateur sélectionné"""
        user = self.get_selected_user()
        if not user:
            QMessageBox.warning(self, "Aucune sélection", "Veuillez sélectionner un utilisateur à modifier.")
            return

        dialog = UserDialog(user=user, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.refresh_data()
            QMessageBox.information(self, "Succès", "Utilisateur modifié avec succès!")

    def toggle_user_status(self):
        """Active/désactive l'utilisateur sélectionné"""
        user = self.get_selected_user()
        if not user:
            QMessageBox.warning(self, "Aucune sélection", "Veuillez sélectionner un utilisateur.")
            return

        # Vérifier qu'on ne désactive pas le dernier admin
        if user.role == "admin" and user.is_active:
            active_admins = [u for u in self.users if u.role == "admin" and u.is_active]
            if len(active_admins) <= 1:
                QMessageBox.warning(
                    self, "Action interdite",
                    "Impossible de désactiver le dernier administrateur actif."
                )
                return

        action = "désactiver" if user.is_active else "activer"
        reply = QMessageBox.question(
            self, "Confirmer l'action",
            f"Êtes-vous sûr de vouloir {action} l'utilisateur '{user.full_name}' ?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # Inverser le statut (à implémenter dans le service)
                user.is_active = not user.is_active
                self.refresh_data()

                status_text = "activé" if user.is_active else "désactivé"
                QMessageBox.information(self, "Succès", f"Utilisateur {status_text} avec succès!")

            except Exception as e:
                QMessageBox.critical(self, "Erreur", f"Erreur lors de la modification: {e}")
