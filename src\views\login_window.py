#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fenêtre de connexion - Bar-Resto Manager
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QFrame, QMessageBox,
                            QApplication)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap, QPalette, QIcon

from utils.config import APP_NAME, APP_VERSION, COMPANY_NAME

class LoginWindow(QWidget):
    """Fenêtre de connexion de l'application"""
    
    # Signal émis lors d'une tentative de connexion
    login_successful = pyqtSignal(str, str)  # username, password
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.setup_styles()
    
    def init_ui(self):
        """Initialise l'interface utilisateur"""
        self.setWindowTitle(f"{APP_NAME} - Connexion")
        self.setFixedSize(400, 500)
        self.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint)
        
        # Layout principal
        main_layout = QVBoxLayout()
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(40, 40, 40, 40)
        
        # En-tête avec logo et titre
        self.create_header(main_layout)
        
        # Formulaire de connexion
        self.create_login_form(main_layout)
        
        # Boutons
        self.create_buttons(main_layout)
        
        # Pied de page
        self.create_footer(main_layout)
        
        self.setLayout(main_layout)
        
        # Centrer la fenêtre
        self.center_window()
    
    def create_header(self, layout):
        """Crée l'en-tête avec logo et titre"""
        header_layout = QVBoxLayout()
        header_layout.setAlignment(Qt.AlignCenter)
        
        # Titre principal
        title_label = QLabel(APP_NAME)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setObjectName("title")
        header_layout.addWidget(title_label)
        
        # Sous-titre
        subtitle_label = QLabel("Système de Gestion de Stock")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setObjectName("subtitle")
        header_layout.addWidget(subtitle_label)
        
        # Ligne de séparation
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        line.setObjectName("separator")
        header_layout.addWidget(line)
        
        layout.addLayout(header_layout)
    
    def create_login_form(self, layout):
        """Crée le formulaire de connexion"""
        form_layout = QVBoxLayout()
        form_layout.setSpacing(15)
        
        # Champ nom d'utilisateur
        username_label = QLabel("Nom d'utilisateur:")
        username_label.setObjectName("field_label")
        form_layout.addWidget(username_label)
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("Entrez votre nom d'utilisateur")
        self.username_input.setObjectName("input_field")
        self.username_input.returnPressed.connect(self.handle_login)
        form_layout.addWidget(self.username_input)
        
        # Champ mot de passe
        password_label = QLabel("Mot de passe:")
        password_label.setObjectName("field_label")
        form_layout.addWidget(password_label)
        
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("Entrez votre mot de passe")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setObjectName("input_field")
        self.password_input.returnPressed.connect(self.handle_login)
        form_layout.addWidget(self.password_input)
        
        layout.addLayout(form_layout)
    
    def create_buttons(self, layout):
        """Crée les boutons de connexion"""
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        
        # Bouton de connexion
        self.login_button = QPushButton("Se connecter")
        self.login_button.setObjectName("primary_button")
        self.login_button.clicked.connect(self.handle_login)
        self.login_button.setDefault(True)
        button_layout.addWidget(self.login_button)
        
        # Bouton quitter
        quit_button = QPushButton("Quitter")
        quit_button.setObjectName("secondary_button")
        quit_button.clicked.connect(self.close)
        button_layout.addWidget(quit_button)
        
        layout.addLayout(button_layout)
    
    def create_footer(self, layout):
        """Crée le pied de page"""
        footer_layout = QVBoxLayout()
        footer_layout.setAlignment(Qt.AlignCenter)
        
        # Informations par défaut
        info_label = QLabel("Compte par défaut:\nUtilisateur: admin\nMot de passe: admin123")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setObjectName("info_label")
        footer_layout.addWidget(info_label)
        
        # Version
        version_label = QLabel(f"Version {APP_VERSION} - {COMPANY_NAME}")
        version_label.setAlignment(Qt.AlignCenter)
        version_label.setObjectName("version_label")
        footer_layout.addWidget(version_label)
        
        layout.addLayout(footer_layout)
    
    def setup_styles(self):
        """Configure les styles CSS"""
        style = """
        QWidget {
            background-color: #f5f5f5;
            font-family: 'Segoe UI', Arial, sans-serif;
        }
        
        #title {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        #subtitle {
            font-size: 14px;
            color: #7f8c8d;
            margin-bottom: 20px;
        }
        
        #separator {
            color: #bdc3c7;
            margin: 10px 0;
        }
        
        #field_label {
            font-size: 12px;
            font-weight: bold;
            color: #34495e;
        }
        
        #input_field {
            padding: 12px;
            border: 2px solid #bdc3c7;
            border-radius: 6px;
            font-size: 14px;
            background-color: white;
        }
        
        #input_field:focus {
            border-color: #3498db;
            outline: none;
        }
        
        #primary_button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: bold;
        }
        
        #primary_button:hover {
            background-color: #2980b9;
        }
        
        #primary_button:pressed {
            background-color: #21618c;
        }
        
        #secondary_button {
            background-color: #95a5a6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 14px;
        }
        
        #secondary_button:hover {
            background-color: #7f8c8d;
        }
        
        #info_label {
            font-size: 11px;
            color: #e74c3c;
            background-color: #fdf2f2;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #f5c6cb;
        }
        
        #version_label {
            font-size: 10px;
            color: #95a5a6;
            margin-top: 10px;
        }
        """
        self.setStyleSheet(style)
    
    def center_window(self):
        """Centre la fenêtre sur l'écran"""
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
    
    def handle_login(self):
        """Gère la tentative de connexion"""
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        if not username or not password:
            QMessageBox.warning(
                self,
                "Champs requis",
                "Veuillez saisir votre nom d'utilisateur et votre mot de passe."
            )
            return
        
        # Désactiver le bouton pendant la vérification
        self.login_button.setEnabled(False)
        self.login_button.setText("Connexion...")
        
        # Émettre le signal pour tenter la connexion
        self.login_successful.emit(username, password)
        
        # Réactiver le bouton
        self.login_button.setEnabled(True)
        self.login_button.setText("Se connecter")
    
    def closeEvent(self, event):
        """Gère la fermeture de la fenêtre"""
        QApplication.quit()
