# 🍽️ Bar-Resto Manager

Application desktop de gestion de stock pour bar-restaurant au Burundi.

## 📋 Fonctionnalités

- **Gestion des stocks** : Suivi des boissons, plats et snacks
- **Système de vente** : Commandes par table avec facturation
- **Approvisionnement** : Gestion des entrées de stock et fournisseurs
- **Alertes automatiques** : Notifications pour stocks bas
- **Gestion des dépenses** : Suivi des charges (gaz, salaires, etc.)
- **Rapports détaillés** : Export PDF/Excel quotidien et mensuel
- **Multi-utilisateurs** : 3 rôles (Admin, Gérant, Serveur)

## 🏗️ Architecture

```
Bar_Resto/
├── main.py                 # Point d'entrée
├── requirements.txt        # Dépendances
├── src/
│   ├── models/            # Modèles de données (SQLAlchemy)
│   ├── views/             # Interfaces PyQt5
│   ├── controllers/       # Logique métier
│   └── utils/             # Utilitaires et configuration
├── data/                  # Base de données SQLite
├── reports/               # Rapports générés
└── resources/             # Images, icônes, etc.
```

## 🚀 Installation

1. Installer Python 3.8+
2. Installer les dépendances :
```bash
pip install -r requirements.txt
```

3. Lancer l'application :
```bash
python main.py
```

## 👥 Rôles et Permissions

| Fonctionnalité | Admin | Gérant | Serveur |
|----------------|-------|--------|---------|
| Connexion | ✅ | ✅ | ✅ |
| Gestion utilisateurs | ✅ | ❌ | ❌ |
| Gestion produits | ✅ | ✅ | ❌ |
| Ventes | ✅ | ✅ | ✅ |
| Historique ventes | ✅ | ✅ | ❌ |
| Approvisionnement | ✅ | ✅ | ❌ |
| Stocks et alertes | ✅ | ✅ | ❌ |
| Rapports | ✅ | ✅ | ❌ |
| Dépenses | ✅ | ✅ | ❌ |
| Suppressions | ✅ | ❌ | ❌ |

## 🔧 Technologies

- **Interface** : PyQt5
- **Base de données** : SQLite + SQLAlchemy
- **Sécurité** : bcrypt pour les mots de passe
- **Rapports** : reportlab (PDF), openpyxl (Excel)
- **Graphiques** : matplotlib
- **Déploiement** : PyInstaller

## 📦 Génération de l'exécutable

```bash
pyinstaller --onefile --noconsole main.py
```

## 🇧🇮 Adaptation Burundi

- Devise : Franc Burundais (BIF)
- Interface en français
- Adaptation aux pratiques locales
