# 🍽️ Bar-Resto Manager

Application desktop complète de gestion de stock pour bar-restaurant au Burundi.

## ✨ Fonctionnalités Complètes

### 🔐 Système d'Authentification
- **Multi-utilisateurs** avec 3 rôles : <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Serveur
- **Sécurité renforcée** avec hachage bcrypt des mots de passe
- **Permissions granulaires** selon les rôles

### 📦 Gestion des Stocks
- **Catalogue produits** avec catégories (boissons, plats, snacks)
- **Suivi en temps réel** des quantités disponibles
- **Alertes automatiques** pour stocks bas et ruptures
- **Gestion des fournisseurs** et historique des livraisons

### 🛒 Point de Vente
- **Interface intuitive** de prise de commande
- **Gestion par table** avec nom du client
- **Calcul automatique** des totaux et taxes
- **Modes de paiement** multiples (espèces, carte, mobile money)

### 💸 Gestion Financière
- **Suivi des dépenses** par catégorie (gaz, salaires, etc.)
- **Calcul automatique** des bénéfices
- **Historique complet** des transactions

### 📊 Rapports et Analyses
- **Rapports quotidiens** et mensuels automatisés
- **Export PDF/CSV** pour comptabilité
- **Statistiques détaillées** des ventes et stocks
- **Graphiques de performance**

### 🚨 Système d'Alertes
- **Surveillance automatique** des stocks critiques
- **Notifications visuelles** dans l'interface
- **Alertes de performance** (ventes faibles, dépenses élevées)

## 🏗️ Architecture Technique

```
Bar_Resto/
├── main.py                    # Point d'entrée principal
├── test_complete.py           # Tests automatisés
├── build_exe.py              # Script de déploiement
├── requirements.txt          # Dépendances Python
├── src/
│   ├── models/               # Couche de données
│   │   ├── database.py       # Modèles SQLAlchemy
│   │   └── services.py       # Services d'accès aux données
│   ├── views/                # Interfaces utilisateur PyQt5
│   │   ├── login_window.py   # Fenêtre de connexion
│   │   ├── main_window.py    # Fenêtre principale
│   │   ├── dashboard_widget.py    # Tableau de bord
│   │   ├── products_widget.py     # Gestion produits
│   │   ├── sales_widget.py        # Point de vente
│   │   ├── stock_widget.py        # Gestion stocks
│   │   ├── expenses_widget.py     # Gestion dépenses
│   │   ├── reports_widget.py      # Génération rapports
│   │   └── users_widget.py        # Gestion utilisateurs
│   ├── controllers/          # Logique métier
│   │   └── main_controller.py     # Contrôleur principal
│   └── utils/                # Utilitaires
│       ├── config.py         # Configuration globale
│       ├── auth.py           # Authentification
│       ├── alerts.py         # Système d'alertes
│       └── reports.py        # Générateur de rapports
├── data/                     # Base de données SQLite
├── reports/                  # Rapports générés
└── resources/                # Ressources (images, etc.)
```

## 🚀 Installation et Démarrage

### Prérequis
- Python 3.8 ou supérieur
- Windows 10/11 (testé et optimisé)

### Installation Développeur
```bash
# Cloner ou télécharger le projet
cd Bar_Resto

# Installer les dépendances
pip install -r requirements.txt

# Tester l'installation
python test_complete.py

# Lancer l'application
python main.py
```

### Génération de l'Exécutable
```bash
# Construire l'exécutable Windows
python build_exe.py

# L'exécutable sera dans le dossier 'dist'
```

## 👥 Système de Rôles et Permissions

| Fonctionnalité | Admin | Gérant | Serveur |
|----------------|-------|--------|---------|
| 🔐 Connexion | ✅ | ✅ | ✅ |
| 👥 Gestion utilisateurs | ✅ | ❌ | ❌ |
| 🍽️ Gestion produits | ✅ | ✅ | ❌ |
| 🛒 Effectuer ventes | ✅ | ✅ | ✅ |
| 📊 Historique ventes | ✅ | ✅ | ❌ |
| 📦 Gestion stocks | ✅ | ✅ | ❌ |
| 📈 Voir stocks/alertes | ✅ | ✅ | ❌ |
| 📋 Générer rapports | ✅ | ✅ | ❌ |
| 💸 Gestion dépenses | ✅ | ✅ | ❌ |
| 🗑️ Suppressions | ✅ | ❌ | ❌ |
| 🔧 Gestion BDD | ✅ | ❌ | ❌ |

## 🔧 Technologies Utilisées

- **Interface** : PyQt5 (interface moderne et responsive)
- **Base de données** : SQLite + SQLAlchemy ORM
- **Sécurité** : bcrypt pour le hachage des mots de passe
- **Rapports** : Génération HTML/CSV intégrée
- **Architecture** : MVC (Model-View-Controller)
- **Déploiement** : PyInstaller pour exécutable Windows

## 📋 Première Utilisation

### Connexion par Défaut
- **Utilisateur** : `admin`
- **Mot de passe** : `admin123`

### Configuration Initiale
1. **Changer le mot de passe admin** (recommandé)
2. **Créer les utilisateurs** (gérants, serveurs)
3. **Ajouter les produits** avec prix et stocks
4. **Configurer les fournisseurs**
5. **Définir les seuils d'alerte** de stock

## 🇧🇮 Adaptation Locale (Burundi)

- **Devise** : Franc Burundais (BIF)
- **Interface** : Entièrement en français
- **Pratiques locales** : Gestion par table, paiement mobile money
- **Rapports** : Format adapté aux besoins comptables locaux

## 🧪 Tests et Qualité

```bash
# Lancer les tests complets
python test_complete.py

# Tests couverts :
# - Imports et dépendances
# - Base de données et modèles
# - Authentification et permissions
# - Services métier
# - Système d'alertes
# - Génération de rapports
```

## 📞 Support et Maintenance

### Sauvegarde des Données
- La base de données se trouve dans `data/bar_resto.db`
- Sauvegardez régulièrement ce fichier
- Les rapports sont dans le dossier `reports/`

### Dépannage
- Vérifiez les permissions d'écriture dans le dossier
- Consultez les logs d'erreur dans la console
- Relancez les tests avec `python test_complete.py`

## 🎯 Fonctionnalités Avancées

- **Tableau de bord** en temps réel avec statistiques
- **Système d'alertes** intelligent et contextuel
- **Génération de rapports** automatisée avec export
- **Interface adaptative** selon les rôles utilisateur
- **Gestion multi-fournisseurs** avec historique
- **Calculs automatiques** de marges et bénéfices

---

**Développé spécialement pour les établissements de restauration au Burundi** 🇧🇮
