#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Widget de gestion des dépenses - Bar-Resto Manager
"""

from datetime import datetime, date
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QPushButton, QTableWidget, QTableWidgetItem,
                            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox,
                            QTextEdit, QDialog, QDialogButtonBox, QMessageBox,
                            QHeaderView, QAbstractItemView, QGroupBox, QFormLayout,
                            QDateEdit, QFrame, QTabWidget)
from PyQt5.QtCore import Qt, pyqtSignal, QDate
from PyQt5.QtGui import QFont, QIcon

from models.database import DatabaseManager
from models.services import ExpenseService
from utils.auth import SessionManager, get_current_user
from utils.config import CURRENCY

class ExpenseDialog(QDialog):
    """Dialog pour ajouter/modifier une dépense"""

    # Catégories prédéfinies pour les dépenses
    EXPENSE_CATEGORIES = [
        "Gaz", "Électricité", "Eau", "Salaires", "Loyer",
        "Approvisionnement", "Transport", "Maintenance",
        "Publicité", "Assurance", "Taxes", "Autres"
    ]

    def __init__(self, expense=None, parent=None):
        super().__init__(parent)
        self.expense = expense
        self.expense_service = ExpenseService(DatabaseManager())
        self.init_ui()

        if expense:
            self.load_expense_data()

    def init_ui(self):
        """Initialise l'interface du dialog"""
        self.setWindowTitle("Nouvelle Dépense" if not self.expense else "Modifier Dépense")
        self.setFixedSize(450, 400)

        layout = QVBoxLayout(self)

        # Formulaire
        form_layout = QFormLayout()

        # Catégorie
        self.category_combo = QComboBox()
        self.category_combo.addItems(self.EXPENSE_CATEGORIES)
        self.category_combo.setEditable(True)  # Permet d'ajouter des catégories personnalisées
        form_layout.addRow("Catégorie:", self.category_combo)

        # Description
        self.description_input = QLineEdit()
        self.description_input.setPlaceholderText("Description de la dépense")
        form_layout.addRow("Description:", self.description_input)

        # Montant
        self.amount_input = QDoubleSpinBox()
        self.amount_input.setRange(0, 9999999)
        self.amount_input.setSuffix(f" {CURRENCY}")
        self.amount_input.setDecimals(0)
        form_layout.addRow("Montant:", self.amount_input)

        # Mode de paiement
        self.payment_combo = QComboBox()
        self.payment_combo.addItems(["Espèces", "Chèque", "Virement", "Carte"])
        form_layout.addRow("Mode de paiement:", self.payment_combo)

        # Numéro de reçu
        self.receipt_input = QLineEdit()
        self.receipt_input.setPlaceholderText("Numéro de reçu/facture (optionnel)")
        form_layout.addRow("N° Reçu:", self.receipt_input)

        # Date
        self.date_input = QDateEdit()
        self.date_input.setDate(QDate.currentDate())
        self.date_input.setCalendarPopup(True)
        form_layout.addRow("Date:", self.date_input)

        # Notes
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(80)
        self.notes_input.setPlaceholderText("Notes supplémentaires (optionnel)")
        form_layout.addRow("Notes:", self.notes_input)

        layout.addLayout(form_layout)

        # Boutons
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        # Style
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QLineEdit, QComboBox, QDoubleSpinBox, QDateEdit, QTextEdit {
                padding: 8px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background-color: white;
            }
            QLineEdit:focus, QComboBox:focus, QDoubleSpinBox:focus, QDateEdit:focus, QTextEdit:focus {
                border-color: #007bff;
            }
        """)

    def load_expense_data(self):
        """Charge les données de la dépense à modifier"""
        if self.expense:
            # Sélectionner la catégorie ou l'ajouter si elle n'existe pas
            category_index = self.category_combo.findText(self.expense.category)
            if category_index >= 0:
                self.category_combo.setCurrentIndex(category_index)
            else:
                self.category_combo.addItem(self.expense.category)
                self.category_combo.setCurrentText(self.expense.category)

            self.description_input.setText(self.expense.description)
            self.amount_input.setValue(self.expense.amount)

            # Mode de paiement
            payment_map = {"cash": "Espèces", "check": "Chèque", "transfer": "Virement", "card": "Carte"}
            payment_text = payment_map.get(self.expense.payment_method, "Espèces")
            payment_index = self.payment_combo.findText(payment_text)
            if payment_index >= 0:
                self.payment_combo.setCurrentIndex(payment_index)

            self.receipt_input.setText(self.expense.receipt_number or "")
            self.date_input.setDate(QDate.fromString(self.expense.expense_date.strftime("%Y-%m-%d"), "yyyy-MM-dd"))
            self.notes_input.setPlainText(self.expense.notes or "")

    def accept(self):
        """Valide et sauvegarde la dépense"""
        category = self.category_combo.currentText().strip()
        description = self.description_input.text().strip()
        amount = self.amount_input.value()

        # Validation
        if not category:
            QMessageBox.warning(self, "Erreur", "La catégorie est requise.")
            return

        if not description:
            QMessageBox.warning(self, "Erreur", "La description est requise.")
            return

        if amount <= 0:
            QMessageBox.warning(self, "Erreur", "Le montant doit être supérieur à 0.")
            return

        try:
            current_user = get_current_user()
            if not current_user:
                QMessageBox.critical(self, "Erreur", "Utilisateur non connecté.")
                return

            # Mapper le mode de paiement
            payment_map = {"Espèces": "cash", "Chèque": "check", "Virement": "transfer", "Carte": "card"}
            payment_method = payment_map[self.payment_combo.currentText()]

            # Date de la dépense
            expense_date = self.date_input.date().toPyDate()
            expense_datetime = datetime.combine(expense_date, datetime.min.time())

            if self.expense:
                # Modification (à implémenter dans le service)
                QMessageBox.information(self, "Info", "Modification des dépenses non implémentée.")
                return
            else:
                # Création
                self.expense = self.expense_service.create_expense(
                    user_id=current_user.id,
                    category=category,
                    description=description,
                    amount=amount,
                    payment_method=payment_method,
                    receipt_number=self.receipt_input.text().strip(),
                    notes=self.notes_input.toPlainText().strip(),
                    expense_date=expense_datetime
                )

            super().accept()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la sauvegarde: {e}")

class ExpensesWidget(QWidget):
    """Widget pour la gestion des dépenses"""

    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.expense_service = ExpenseService(self.db_manager)
        self.session_manager = SessionManager()

        self.init_ui()
        self.setup_styles()
        self.refresh_data()

    def init_ui(self):
        """Initialise l'interface utilisateur"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)

        # Titre
        title_label = QLabel("💸 Gestion des Dépenses")
        title_label.setObjectName("page_title")
        layout.addWidget(title_label)

        # Onglets
        self.tab_widget = QTabWidget()

        # Onglet Nouvelle dépense
        self.create_new_expense_tab()

        # Onglet Historique
        self.create_history_tab()

        # Onglet Statistiques
        self.create_statistics_tab()

        layout.addWidget(self.tab_widget)

    def create_new_expense_tab(self):
        """Crée l'onglet de nouvelle dépense"""
        new_expense_widget = QWidget()
        layout = QVBoxLayout(new_expense_widget)

        # Barre d'outils
        toolbar_layout = QHBoxLayout()

        # Bouton nouvelle dépense
        new_expense_button = QPushButton("➕ Nouvelle Dépense")
        new_expense_button.setObjectName("primary_button")
        new_expense_button.clicked.connect(self.add_expense)
        toolbar_layout.addWidget(new_expense_button)

        toolbar_layout.addStretch()

        layout.addLayout(toolbar_layout)

        # Formulaire rapide
        quick_form_group = QGroupBox("Enregistrement Rapide")
        quick_form_layout = QFormLayout(quick_form_group)

        # Catégorie rapide
        self.quick_category_combo = QComboBox()
        self.quick_category_combo.addItems(ExpenseDialog.EXPENSE_CATEGORIES)
        quick_form_layout.addRow("Catégorie:", self.quick_category_combo)

        # Description rapide
        self.quick_description_input = QLineEdit()
        self.quick_description_input.setPlaceholderText("Description de la dépense")
        quick_form_layout.addRow("Description:", self.quick_description_input)

        # Montant rapide
        self.quick_amount_input = QDoubleSpinBox()
        self.quick_amount_input.setRange(0, 999999)
        self.quick_amount_input.setSuffix(f" {CURRENCY}")
        self.quick_amount_input.setDecimals(0)
        quick_form_layout.addRow("Montant:", self.quick_amount_input)

        # Bouton d'ajout rapide
        quick_add_button = QPushButton("Enregistrer")
        quick_add_button.setObjectName("secondary_button")
        quick_add_button.clicked.connect(self.quick_add_expense)
        quick_form_layout.addRow("", quick_add_button)

        layout.addWidget(quick_form_group)

        # Dépenses récentes
        recent_group = QGroupBox("Dépenses Récentes")
        recent_layout = QVBoxLayout(recent_group)

        self.recent_expenses_table = QTableWidget()
        self.recent_expenses_table.setColumnCount(5)
        self.recent_expenses_table.setHorizontalHeaderLabels([
            "Date", "Catégorie", "Description", "Montant", "Mode"
        ])
        self.recent_expenses_table.setMaximumHeight(200)
        self.recent_expenses_table.setAlternatingRowColors(True)

        # Configuration du tableau
        header = self.recent_expenses_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # Description

        recent_layout.addWidget(self.recent_expenses_table)
        layout.addWidget(recent_group)

        layout.addStretch()

        self.tab_widget.addTab(new_expense_widget, "Nouvelle Dépense")

    def create_history_tab(self):
        """Crée l'onglet d'historique des dépenses"""
        history_widget = QWidget()
        layout = QVBoxLayout(history_widget)

        # Filtres
        filters_layout = QHBoxLayout()

        # Filtre par période
        filters_layout.addWidget(QLabel("Période:"))
        self.period_filter = QComboBox()
        self.period_filter.addItems([
            "Aujourd'hui", "Cette semaine", "Ce mois", "Mois dernier"
        ])
        self.period_filter.currentTextChanged.connect(self.filter_expenses)
        filters_layout.addWidget(self.period_filter)

        # Filtre par catégorie
        filters_layout.addWidget(QLabel("Catégorie:"))
        self.category_filter = QComboBox()
        self.category_filter.addItem("Toutes")
        self.category_filter.addItems(ExpenseDialog.EXPENSE_CATEGORIES)
        self.category_filter.currentTextChanged.connect(self.filter_expenses)
        filters_layout.addWidget(self.category_filter)

        filters_layout.addStretch()

        # Bouton actualiser
        refresh_button = QPushButton("🔄 Actualiser")
        refresh_button.clicked.connect(self.refresh_data)
        filters_layout.addWidget(refresh_button)

        layout.addLayout(filters_layout)

        # Tableau des dépenses
        self.expenses_table = QTableWidget()
        self.expenses_table.setColumnCount(7)
        self.expenses_table.setHorizontalHeaderLabels([
            "Date", "Catégorie", "Description", "Montant", "Mode", "Reçu", "Utilisateur"
        ])

        # Configuration du tableau
        header = self.expenses_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # Description

        self.expenses_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.expenses_table.setAlternatingRowColors(True)
        self.expenses_table.setSortingEnabled(True)

        layout.addWidget(self.expenses_table)

        # Résumé
        summary_group = QGroupBox("Résumé de la Période")
        summary_layout = QGridLayout(summary_group)

        self.period_total_label = QLabel(f"0 {CURRENCY}")
        self.period_total_label.setObjectName("stat_value")
        summary_layout.addWidget(QLabel("Total période:"), 0, 0)
        summary_layout.addWidget(self.period_total_label, 0, 1)

        self.period_count_label = QLabel("0")
        self.period_count_label.setObjectName("stat_value")
        summary_layout.addWidget(QLabel("Nombre de dépenses:"), 0, 2)
        summary_layout.addWidget(self.period_count_label, 0, 3)

        self.avg_expense_label = QLabel(f"0 {CURRENCY}")
        self.avg_expense_label.setObjectName("stat_value")
        summary_layout.addWidget(QLabel("Dépense moyenne:"), 1, 0)
        summary_layout.addWidget(self.avg_expense_label, 1, 1)

        layout.addWidget(summary_group)

        self.tab_widget.addTab(history_widget, "Historique")

    def create_statistics_tab(self):
        """Crée l'onglet des statistiques"""
        stats_widget = QWidget()
        layout = QVBoxLayout(stats_widget)

        # Statistiques par catégorie
        category_group = QGroupBox("Dépenses par Catégorie (Ce Mois)")
        category_layout = QVBoxLayout(category_group)

        self.category_stats_table = QTableWidget()
        self.category_stats_table.setColumnCount(3)
        self.category_stats_table.setHorizontalHeaderLabels([
            "Catégorie", "Montant", "Pourcentage"
        ])

        # Configuration du tableau
        header = self.category_stats_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # Catégorie

        self.category_stats_table.setAlternatingRowColors(True)
        category_layout.addWidget(self.category_stats_table)

        layout.addWidget(category_group)

        # Statistiques mensuelles
        monthly_group = QGroupBox("Évolution Mensuelle")
        monthly_layout = QVBoxLayout(monthly_group)

        # Tableau simple pour les 6 derniers mois
        self.monthly_stats_table = QTableWidget()
        self.monthly_stats_table.setColumnCount(2)
        self.monthly_stats_table.setHorizontalHeaderLabels([
            "Mois", "Total Dépenses"
        ])
        self.monthly_stats_table.setMaximumHeight(200)
        self.monthly_stats_table.setAlternatingRowColors(True)

        monthly_layout.addWidget(self.monthly_stats_table)
        layout.addWidget(monthly_group)

        layout.addStretch()

        self.tab_widget.addTab(stats_widget, "Statistiques")

    def setup_styles(self):
        """Configure les styles CSS"""
        style = """
        #page_title {
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        #primary_button {
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            font-weight: bold;
            font-size: 14px;
        }

        #primary_button:hover {
            background-color: #c82333;
        }

        #secondary_button {
            background-color: #6c757d;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
        }

        #secondary_button:hover {
            background-color: #545b62;
        }

        #stat_value {
            font-size: 18px;
            font-weight: bold;
            color: #dc3545;
        }

        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 5px;
            margin-top: 10px;
            padding-top: 10px;
        }

        QTableWidget {
            gridline-color: #dee2e6;
            background-color: white;
            alternate-background-color: #f8f9fa;
        }

        QTableWidget::item {
            padding: 8px;
        }

        QHeaderView::section {
            background-color: #e9ecef;
            padding: 8px;
            border: none;
            font-weight: bold;
        }
        """
        self.setStyleSheet(style)

    def refresh_data(self):
        """Actualise toutes les données"""
        try:
            self.refresh_recent_expenses()
            self.refresh_expenses_history()
            self.refresh_statistics()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'actualisation: {e}")

    def add_expense(self):
        """Ajoute une nouvelle dépense"""
        dialog = ExpenseDialog(parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.refresh_data()
            QMessageBox.information(self, "Succès", "Dépense enregistrée avec succès!")

    def quick_add_expense(self):
        """Ajoute rapidement une dépense"""
        category = self.quick_category_combo.currentText()
        description = self.quick_description_input.text().strip()
        amount = self.quick_amount_input.value()

        if not description:
            QMessageBox.warning(self, "Erreur", "La description est requise.")
            return

        if amount <= 0:
            QMessageBox.warning(self, "Erreur", "Le montant doit être supérieur à 0.")
            return

        try:
            current_user = get_current_user()
            if not current_user:
                QMessageBox.critical(self, "Erreur", "Utilisateur non connecté.")
                return

            # Créer la dépense
            expense = self.expense_service.create_expense(
                user_id=current_user.id,
                category=category,
                description=description,
                amount=amount,
                payment_method="cash",  # Par défaut espèces
                notes="Ajout rapide"
            )

            if expense:
                # Réinitialiser le formulaire
                self.quick_description_input.clear()
                self.quick_amount_input.setValue(0)

                # Actualiser les données
                self.refresh_data()

                QMessageBox.information(
                    self, "Succès",
                    f"Dépense de {amount:,.0f} {CURRENCY} enregistrée!"
                )
            else:
                QMessageBox.critical(self, "Erreur", "Erreur lors de l'enregistrement.")

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur: {e}")

    def refresh_recent_expenses(self):
        """Actualise les dépenses récentes"""
        try:
            today = date.today()
            expenses = self.expense_service.get_expenses_by_date(today)

            # Trier par date décroissante
            expenses.sort(key=lambda x: x.created_at, reverse=True)

            # Prendre les 10 plus récentes
            recent_expenses = expenses[:10]

            self.recent_expenses_table.setRowCount(len(recent_expenses))

            for row, expense in enumerate(recent_expenses):
                # Date
                date_str = expense.expense_date.strftime("%H:%M")
                self.recent_expenses_table.setItem(row, 0, QTableWidgetItem(date_str))

                # Catégorie
                self.recent_expenses_table.setItem(row, 1, QTableWidgetItem(expense.category))

                # Description
                self.recent_expenses_table.setItem(row, 2, QTableWidgetItem(expense.description))

                # Montant
                amount_item = QTableWidgetItem(f"{expense.amount:,.0f} {CURRENCY}")
                amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                self.recent_expenses_table.setItem(row, 3, amount_item)

                # Mode de paiement
                payment_map = {"cash": "Espèces", "check": "Chèque", "transfer": "Virement", "card": "Carte"}
                payment_text = payment_map.get(expense.payment_method, expense.payment_method)
                self.recent_expenses_table.setItem(row, 4, QTableWidgetItem(payment_text))

        except Exception as e:
            print(f"Erreur lors de l'actualisation des dépenses récentes: {e}")

    def refresh_expenses_history(self):
        """Actualise l'historique des dépenses"""
        # Cette méthode sera implémentée avec les filtres
        pass

    def refresh_statistics(self):
        """Actualise les statistiques"""
        # Cette méthode sera implémentée avec les calculs statistiques
        pass

    def filter_expenses(self):
        """Filtre les dépenses selon les critères sélectionnés"""
        # Cette méthode sera implémentée avec les filtres
        pass
