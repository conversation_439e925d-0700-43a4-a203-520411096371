#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de lancement de l'application Bar-Resto Manager
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    """Lance l'application Bar-Resto Manager"""
    print("🚀 Lancement de Bar-Resto Manager...")
    
    # Configuration High DPI AVANT de créer l'application
    if hasattr(Qt, 'AA_EnableHighDpiScaling'):
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

    app = QApplication(sys.argv)
    app.setApplicationName("Bar-Resto Manager")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("BarResto Solutions")
    
    print("📱 Application Qt créée")
    
    # Importer après la création de l'application
    from controllers.main_controller import MainController
    from models.database import DatabaseManager
    
    print("🗄️ Initialisation de la base de données...")
    db_manager = DatabaseManager()
    db_manager.init_database()
    print("✅ Base de données initialisée")
    
    print("🎮 Création du contrôleur principal...")
    controller = MainController()
    
    print("🔐 Affichage de la fenêtre de connexion...")
    controller.show_login()
    
    print("✅ Application prête ! Fenêtre de connexion affichée.")
    print("📋 Connexion par défaut: admin / admin123")
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
