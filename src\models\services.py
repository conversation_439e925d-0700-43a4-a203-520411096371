#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Services pour l'accès aux données - Bar-Resto Manager
"""

from datetime import datetime, date
from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_

from .database import DatabaseManager, User, Product, Supplier, StockMovement, Sale, SaleItem, Expense
from utils.config import UserRole, ProductCategory

class UserService:
    """Service pour la gestion des utilisateurs"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
    
    def authenticate(self, username: str, password: str) -> Optional[User]:
        """Authentifie un utilisateur"""
        session = self.db_manager.get_session()
        try:
            user = session.query(User).filter_by(username=username, is_active=True).first()
            if user and user.check_password(password):
                user.last_login = datetime.utcnow()
                session.commit()

                # Créer une copie détachée pour éviter les problèmes de session
                user_data = {
                    'id': user.id,
                    'username': user.username,
                    'full_name': user.full_name,
                    'role': user.role,
                    'is_active': user.is_active,
                    'created_at': user.created_at,
                    'last_login': user.last_login
                }

                # Créer un nouvel objet User détaché
                detached_user = User()
                for key, value in user_data.items():
                    setattr(detached_user, key, value)

                return detached_user
            return None
        finally:
            session.close()
    
    def create_user(self, username: str, password: str, full_name: str, role: str) -> User:
        """Crée un nouvel utilisateur"""
        session = self.db_manager.get_session()
        try:
            user = User(username=username, full_name=full_name, role=role)
            user.set_password(password)
            session.add(user)
            session.commit()
            session.refresh(user)
            return user
        finally:
            session.close()
    
    def get_all_users(self) -> List[User]:
        """Récupère tous les utilisateurs"""
        session = self.db_manager.get_session()
        try:
            return session.query(User).filter_by(is_active=True).all()
        finally:
            session.close()

class ProductService:
    """Service pour la gestion des produits"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
    
    def create_product(self, name: str, category: str, purchase_price: float, 
                      selling_price: float, initial_stock: int = 0, 
                      min_stock_alert: int = 5, description: str = "") -> Product:
        """Crée un nouveau produit"""
        session = self.db_manager.get_session()
        try:
            product = Product(
                name=name,
                category=category,
                purchase_price=purchase_price,
                selling_price=selling_price,
                current_stock=initial_stock,
                min_stock_alert=min_stock_alert,
                description=description
            )
            session.add(product)
            session.commit()
            session.refresh(product)
            return product
        finally:
            session.close()
    
    def get_all_products(self, active_only: bool = True) -> List[Product]:
        """Récupère tous les produits"""
        session = self.db_manager.get_session()
        try:
            query = session.query(Product)
            if active_only:
                query = query.filter_by(is_active=True)
            return query.all()
        finally:
            session.close()
    
    def get_products_by_category(self, category: str) -> List[Product]:
        """Récupère les produits par catégorie"""
        session = self.db_manager.get_session()
        try:
            return session.query(Product).filter_by(category=category, is_active=True).all()
        finally:
            session.close()
    
    def get_low_stock_products(self) -> List[Product]:
        """Récupère les produits avec stock bas"""
        session = self.db_manager.get_session()
        try:
            return session.query(Product).filter(
                and_(Product.current_stock <= Product.min_stock_alert, Product.is_active == True)
            ).all()
        finally:
            session.close()
    
    def update_stock(self, product_id: int, quantity_change: int, movement_type: str, 
                    supplier_id: Optional[int] = None, unit_price: Optional[float] = None,
                    notes: str = "") -> bool:
        """Met à jour le stock d'un produit"""
        session = self.db_manager.get_session()
        try:
            product = session.query(Product).get(product_id)
            if not product:
                return False
            
            # Mettre à jour le stock
            if movement_type == 'entree':
                product.current_stock += quantity_change
            elif movement_type == 'sortie':
                if product.current_stock >= quantity_change:
                    product.current_stock -= quantity_change
                else:
                    return False  # Stock insuffisant
            
            # Enregistrer le mouvement
            movement = StockMovement(
                product_id=product_id,
                supplier_id=supplier_id,
                movement_type=movement_type,
                quantity=quantity_change,
                unit_price=unit_price,
                total_cost=unit_price * quantity_change if unit_price else None,
                notes=notes
            )
            session.add(movement)
            session.commit()
            return True
        except Exception:
            session.rollback()
            return False
        finally:
            session.close()

class SaleService:
    """Service pour la gestion des ventes"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self.product_service = ProductService(db_manager)
    
    def create_sale(self, user_id: int, items: List[dict], table_number: str = "", 
                   customer_name: str = "", payment_method: str = "cash", 
                   notes: str = "") -> Optional[Sale]:
        """Crée une nouvelle vente"""
        session = self.db_manager.get_session()
        try:
            # Calculer le total
            total_amount = 0
            sale_items = []
            
            for item in items:
                product = session.query(Product).get(item['product_id'])
                if not product or product.current_stock < item['quantity']:
                    session.rollback()
                    return None  # Produit inexistant ou stock insuffisant
                
                item_total = item['quantity'] * product.selling_price
                total_amount += item_total
                
                sale_items.append({
                    'product_id': item['product_id'],
                    'quantity': item['quantity'],
                    'unit_price': product.selling_price,
                    'total_price': item_total
                })
            
            # Créer la vente
            sale = Sale(
                user_id=user_id,
                table_number=table_number,
                customer_name=customer_name,
                total_amount=total_amount,
                payment_method=payment_method,
                notes=notes
            )
            session.add(sale)
            session.flush()  # Pour obtenir l'ID de la vente
            
            # Ajouter les articles et mettre à jour les stocks
            for item_data in sale_items:
                sale_item = SaleItem(
                    sale_id=sale.id,
                    **item_data
                )
                session.add(sale_item)
                
                # Décrémenter le stock
                product = session.query(Product).get(item_data['product_id'])
                product.current_stock -= item_data['quantity']
            
            session.commit()
            session.refresh(sale)
            return sale
        except Exception:
            session.rollback()
            return None
        finally:
            session.close()
    
    def get_sales_by_date(self, target_date: date) -> List[Sale]:
        """Récupère les ventes d'une date donnée"""
        session = self.db_manager.get_session()
        try:
            start_date = datetime.combine(target_date, datetime.min.time())
            end_date = datetime.combine(target_date, datetime.max.time())
            
            return session.query(Sale).filter(
                and_(Sale.created_at >= start_date, Sale.created_at <= end_date)
            ).all()
        finally:
            session.close()
    
    def get_daily_sales_total(self, target_date: date) -> float:
        """Calcule le total des ventes d'une journée"""
        session = self.db_manager.get_session()
        try:
            start_date = datetime.combine(target_date, datetime.min.time())
            end_date = datetime.combine(target_date, datetime.max.time())
            
            result = session.query(func.sum(Sale.total_amount)).filter(
                and_(Sale.created_at >= start_date, Sale.created_at <= end_date)
            ).scalar()
            
            return result or 0.0
        finally:
            session.close()

class ExpenseService:
    """Service pour la gestion des dépenses"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
    
    def create_expense(self, user_id: int, category: str, description: str, 
                      amount: float, payment_method: str = "cash", 
                      receipt_number: str = "", notes: str = "",
                      expense_date: Optional[datetime] = None) -> Expense:
        """Crée une nouvelle dépense"""
        session = self.db_manager.get_session()
        try:
            expense = Expense(
                user_id=user_id,
                category=category,
                description=description,
                amount=amount,
                payment_method=payment_method,
                receipt_number=receipt_number,
                notes=notes,
                expense_date=expense_date or datetime.utcnow()
            )
            session.add(expense)
            session.commit()
            session.refresh(expense)
            return expense
        finally:
            session.close()
    
    def get_expenses_by_date(self, target_date: date) -> List[Expense]:
        """Récupère les dépenses d'une date donnée"""
        session = self.db_manager.get_session()
        try:
            start_date = datetime.combine(target_date, datetime.min.time())
            end_date = datetime.combine(target_date, datetime.max.time())
            
            return session.query(Expense).filter(
                and_(Expense.expense_date >= start_date, Expense.expense_date <= end_date)
            ).all()
        finally:
            session.close()
    
    def get_monthly_expenses_total(self, year: int, month: int) -> float:
        """Calcule le total des dépenses d'un mois"""
        session = self.db_manager.get_session()
        try:
            result = session.query(func.sum(Expense.amount)).filter(
                and_(
                    func.extract('year', Expense.expense_date) == year,
                    func.extract('month', Expense.expense_date) == month
                )
            ).scalar()
            
            return result or 0.0
        finally:
            session.close()
