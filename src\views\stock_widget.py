#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Widget de gestion du stock - Bar-Resto Manager
"""

from datetime import datetime, date
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QPushButton, QTableWidget, QTableWidgetItem,
                            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox,
                            QTextEdit, QDialog, QDialogButtonBox, QMessageBox,
                            QHeaderView, QAbstractItemView, QGroupBox, QFormLayout,
                            QTabWidget, QDateEdit, QFrame)
from PyQt5.QtCore import Qt, pyqtSignal, QDate
from PyQt5.QtGui import QFont, QIcon

from models.database import DatabaseManager, Supplier
from models.services import ProductService
from utils.auth import SessionManager
from utils.config import CURRENCY

class SupplierDialog(QDialog):
    """Dialog pour ajouter/modifier un fournisseur"""

    def __init__(self, supplier=None, parent=None):
        super().__init__(parent)
        self.supplier = supplier
        self.db_manager = DatabaseManager()
        self.init_ui()

        if supplier:
            self.load_supplier_data()

    def init_ui(self):
        """Initialise l'interface du dialog"""
        self.setWindowTitle("Nouveau Fournisseur" if not self.supplier else "Modifier Fournisseur")
        self.setFixedSize(400, 350)

        layout = QVBoxLayout(self)

        # Formulaire
        form_layout = QFormLayout()

        # Nom du fournisseur
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("Nom du fournisseur")
        form_layout.addRow("Nom:", self.name_input)

        # Personne de contact
        self.contact_input = QLineEdit()
        self.contact_input.setPlaceholderText("Nom du contact")
        form_layout.addRow("Contact:", self.contact_input)

        # Téléphone
        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("+257 XX XX XX XX")
        form_layout.addRow("Téléphone:", self.phone_input)

        # Email
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("<EMAIL>")
        form_layout.addRow("Email:", self.email_input)

        # Adresse
        self.address_input = QTextEdit()
        self.address_input.setMaximumHeight(80)
        self.address_input.setPlaceholderText("Adresse complète")
        form_layout.addRow("Adresse:", self.address_input)

        layout.addLayout(form_layout)

        # Boutons
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

    def load_supplier_data(self):
        """Charge les données du fournisseur à modifier"""
        if self.supplier:
            self.name_input.setText(self.supplier.name)
            self.contact_input.setText(self.supplier.contact_person or "")
            self.phone_input.setText(self.supplier.phone or "")
            self.email_input.setText(self.supplier.email or "")
            self.address_input.setPlainText(self.supplier.address or "")

    def accept(self):
        """Valide et sauvegarde le fournisseur"""
        name = self.name_input.text().strip()

        if not name:
            QMessageBox.warning(self, "Erreur", "Le nom du fournisseur est requis.")
            return

        try:
            session = self.db_manager.get_session()

            if self.supplier:
                # Modification
                self.supplier.name = name
                self.supplier.contact_person = self.contact_input.text().strip()
                self.supplier.phone = self.phone_input.text().strip()
                self.supplier.email = self.email_input.text().strip()
                self.supplier.address = self.address_input.toPlainText().strip()
            else:
                # Création
                self.supplier = Supplier(
                    name=name,
                    contact_person=self.contact_input.text().strip(),
                    phone=self.phone_input.text().strip(),
                    email=self.email_input.text().strip(),
                    address=self.address_input.toPlainText().strip()
                )
                session.add(self.supplier)

            session.commit()
            session.close()
            super().accept()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la sauvegarde: {e}")

class StockEntryDialog(QDialog):
    """Dialog pour ajouter une entrée de stock"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self.product_service = ProductService(self.db_manager)
        self.init_ui()
        self.load_data()

    def init_ui(self):
        """Initialise l'interface du dialog"""
        self.setWindowTitle("Nouvelle Entrée de Stock")
        self.setFixedSize(450, 400)

        layout = QVBoxLayout(self)

        # Formulaire
        form_layout = QFormLayout()

        # Produit
        self.product_combo = QComboBox()
        form_layout.addRow("Produit:", self.product_combo)

        # Fournisseur
        self.supplier_combo = QComboBox()
        form_layout.addRow("Fournisseur:", self.supplier_combo)

        # Quantité
        self.quantity_input = QSpinBox()
        self.quantity_input.setRange(1, 99999)
        self.quantity_input.setValue(1)
        form_layout.addRow("Quantité:", self.quantity_input)

        # Prix unitaire
        self.unit_price_input = QDoubleSpinBox()
        self.unit_price_input.setRange(0, 999999)
        self.unit_price_input.setSuffix(f" {CURRENCY}")
        self.unit_price_input.valueChanged.connect(self.calculate_total)
        form_layout.addRow("Prix unitaire:", self.unit_price_input)

        # Total
        self.total_label = QLabel(f"0 {CURRENCY}")
        self.total_label.setObjectName("total_amount")
        form_layout.addRow("Total:", self.total_label)

        # Date
        self.date_input = QDateEdit()
        self.date_input.setDate(QDate.currentDate())
        self.date_input.setCalendarPopup(True)
        form_layout.addRow("Date:", self.date_input)

        # Notes
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(80)
        self.notes_input.setPlaceholderText("Notes sur la livraison (optionnel)")
        form_layout.addRow("Notes:", self.notes_input)

        layout.addLayout(form_layout)

        # Boutons
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        # Connecter le signal de changement de quantité
        self.quantity_input.valueChanged.connect(self.calculate_total)

    def load_data(self):
        """Charge les produits et fournisseurs"""
        try:
            session = self.db_manager.get_session()

            # Charger les produits
            products = self.product_service.get_all_products()
            self.product_combo.clear()
            for product in products:
                self.product_combo.addItem(product.name, product)

            # Charger les fournisseurs
            suppliers = session.query(Supplier).filter_by(is_active=True).all()
            self.supplier_combo.clear()
            self.supplier_combo.addItem("Aucun fournisseur", None)
            for supplier in suppliers:
                self.supplier_combo.addItem(supplier.name, supplier)

            session.close()

        except Exception as e:
            print(f"Erreur lors du chargement des données: {e}")

    def calculate_total(self):
        """Calcule le total"""
        quantity = self.quantity_input.value()
        unit_price = self.unit_price_input.value()
        total = quantity * unit_price
        self.total_label.setText(f"{total:,.0f} {CURRENCY}")

    def accept(self):
        """Valide et enregistre l'entrée de stock"""
        product = self.product_combo.currentData()
        supplier = self.supplier_combo.currentData()
        quantity = self.quantity_input.value()
        unit_price = self.unit_price_input.value()

        if not product:
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner un produit.")
            return

        if quantity <= 0:
            QMessageBox.warning(self, "Erreur", "La quantité doit être supérieure à 0.")
            return

        try:
            # Enregistrer l'entrée de stock
            success = self.product_service.update_stock(
                product_id=product.id,
                quantity_change=quantity,
                movement_type='entree',
                supplier_id=supplier.id if supplier else None,
                unit_price=unit_price,
                notes=self.notes_input.toPlainText().strip()
            )

            if success:
                super().accept()
            else:
                QMessageBox.critical(self, "Erreur", "Erreur lors de l'enregistrement de l'entrée.")

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement: {e}")

class StockWidget(QWidget):
    """Widget pour la gestion du stock et des approvisionnements"""

    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.product_service = ProductService(self.db_manager)
        self.session_manager = SessionManager()

        self.init_ui()
        self.setup_styles()
        self.refresh_data()

    def init_ui(self):
        """Initialise l'interface utilisateur"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)

        # Titre
        title_label = QLabel("📦 Gestion du Stock")
        title_label.setObjectName("page_title")
        layout.addWidget(title_label)

        # Onglets
        self.tab_widget = QTabWidget()

        # Onglet État du stock
        self.create_stock_status_tab()

        # Onglet Approvisionnement
        if self.session_manager.has_permission('manage_stock'):
            self.create_supply_tab()

        # Onglet Fournisseurs
        if self.session_manager.has_permission('manage_stock'):
            self.create_suppliers_tab()

        # Onglet Mouvements
        self.create_movements_tab()

        layout.addWidget(self.tab_widget)

    def create_stock_status_tab(self):
        """Crée l'onglet d'état du stock"""
        stock_widget = QWidget()
        layout = QVBoxLayout(stock_widget)

        # Filtres et actions
        toolbar_layout = QHBoxLayout()

        # Filtre par statut
        toolbar_layout.addWidget(QLabel("Statut:"))
        self.status_filter = QComboBox()
        self.status_filter.addItems(["Tous", "Stock bas", "Rupture", "En stock"])
        self.status_filter.currentTextChanged.connect(self.filter_stock)
        toolbar_layout.addWidget(self.status_filter)

        # Recherche
        toolbar_layout.addWidget(QLabel("Recherche:"))
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Nom du produit...")
        self.search_input.textChanged.connect(self.filter_stock)
        toolbar_layout.addWidget(self.search_input)

        toolbar_layout.addStretch()

        # Bouton actualiser
        refresh_button = QPushButton("🔄 Actualiser")
        refresh_button.clicked.connect(self.refresh_data)
        toolbar_layout.addWidget(refresh_button)

        layout.addLayout(toolbar_layout)

        # Tableau du stock
        self.stock_table = QTableWidget()
        self.stock_table.setColumnCount(7)
        self.stock_table.setHorizontalHeaderLabels([
            "Produit", "Catégorie", "Stock Actuel", "Seuil", "Valeur Stock", "Dernière Entrée", "Statut"
        ])

        # Configuration du tableau
        header = self.stock_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # Produit

        self.stock_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.stock_table.setAlternatingRowColors(True)
        self.stock_table.setSortingEnabled(True)

        layout.addWidget(self.stock_table)

        # Statistiques
        stats_group = QGroupBox("Statistiques du Stock")
        stats_layout = QGridLayout(stats_group)

        self.total_products_label = QLabel("0")
        self.total_products_label.setObjectName("stat_value")
        stats_layout.addWidget(QLabel("Total produits:"), 0, 0)
        stats_layout.addWidget(self.total_products_label, 0, 1)

        self.low_stock_label = QLabel("0")
        self.low_stock_label.setObjectName("stat_value_warning")
        stats_layout.addWidget(QLabel("Stock bas:"), 0, 2)
        stats_layout.addWidget(self.low_stock_label, 0, 3)

        self.out_of_stock_label = QLabel("0")
        self.out_of_stock_label.setObjectName("stat_value_danger")
        stats_layout.addWidget(QLabel("Ruptures:"), 1, 0)
        stats_layout.addWidget(self.out_of_stock_label, 1, 1)

        self.total_value_label = QLabel(f"0 {CURRENCY}")
        self.total_value_label.setObjectName("stat_value")
        stats_layout.addWidget(QLabel("Valeur totale:"), 1, 2)
        stats_layout.addWidget(self.total_value_label, 1, 3)

        layout.addWidget(stats_group)

        self.tab_widget.addTab(stock_widget, "État du Stock")

    def create_supply_tab(self):
        """Crée l'onglet d'approvisionnement"""
        supply_widget = QWidget()
        layout = QVBoxLayout(supply_widget)

        # Barre d'outils
        toolbar_layout = QHBoxLayout()

        # Bouton nouvelle entrée
        new_entry_button = QPushButton("➕ Nouvelle Entrée")
        new_entry_button.setObjectName("primary_button")
        new_entry_button.clicked.connect(self.add_stock_entry)
        toolbar_layout.addWidget(new_entry_button)

        toolbar_layout.addStretch()

        layout.addLayout(toolbar_layout)

        # Message d'information
        info_label = QLabel("💡 Utilisez cette section pour enregistrer les livraisons et approvisionnements.")
        info_label.setObjectName("info_label")
        layout.addWidget(info_label)

        # Formulaire rapide
        quick_form_group = QGroupBox("Entrée Rapide")
        quick_form_layout = QFormLayout(quick_form_group)

        self.quick_product_combo = QComboBox()
        quick_form_layout.addRow("Produit:", self.quick_product_combo)

        self.quick_quantity_input = QSpinBox()
        self.quick_quantity_input.setRange(1, 9999)
        quick_form_layout.addRow("Quantité:", self.quick_quantity_input)

        quick_add_button = QPushButton("Ajouter au Stock")
        quick_add_button.setObjectName("secondary_button")
        quick_add_button.clicked.connect(self.quick_add_stock)
        quick_form_layout.addRow("", quick_add_button)

        layout.addWidget(quick_form_group)

        layout.addStretch()

        self.tab_widget.addTab(supply_widget, "Approvisionnement")

    def create_suppliers_tab(self):
        """Crée l'onglet des fournisseurs"""
        suppliers_widget = QWidget()
        layout = QVBoxLayout(suppliers_widget)

        # Barre d'outils
        toolbar_layout = QHBoxLayout()

        # Bouton nouveau fournisseur
        new_supplier_button = QPushButton("➕ Nouveau Fournisseur")
        new_supplier_button.setObjectName("primary_button")
        new_supplier_button.clicked.connect(self.add_supplier)
        toolbar_layout.addWidget(new_supplier_button)

        # Bouton modifier
        self.edit_supplier_button = QPushButton("✏️ Modifier")
        self.edit_supplier_button.setObjectName("secondary_button")
        self.edit_supplier_button.clicked.connect(self.edit_supplier)
        self.edit_supplier_button.setEnabled(False)
        toolbar_layout.addWidget(self.edit_supplier_button)

        toolbar_layout.addStretch()

        layout.addLayout(toolbar_layout)

        # Tableau des fournisseurs
        self.suppliers_table = QTableWidget()
        self.suppliers_table.setColumnCount(5)
        self.suppliers_table.setHorizontalHeaderLabels([
            "Nom", "Contact", "Téléphone", "Email", "Dernière Livraison"
        ])

        # Configuration du tableau
        header = self.suppliers_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # Nom

        self.suppliers_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.suppliers_table.setAlternatingRowColors(True)
        self.suppliers_table.selectionModel().selectionChanged.connect(self.on_supplier_selection_changed)

        layout.addWidget(self.suppliers_table)

        self.tab_widget.addTab(suppliers_widget, "Fournisseurs")

    def create_movements_tab(self):
        """Crée l'onglet des mouvements de stock"""
        movements_widget = QWidget()
        layout = QVBoxLayout(movements_widget)

        # Filtres
        filters_layout = QHBoxLayout()

        filters_layout.addWidget(QLabel("Période:"))
        self.period_filter = QComboBox()
        self.period_filter.addItems(["Aujourd'hui", "Cette semaine", "Ce mois"])
        self.period_filter.currentTextChanged.connect(self.filter_movements)
        filters_layout.addWidget(self.period_filter)

        filters_layout.addWidget(QLabel("Type:"))
        self.movement_type_filter = QComboBox()
        self.movement_type_filter.addItems(["Tous", "Entrées", "Sorties"])
        self.movement_type_filter.currentTextChanged.connect(self.filter_movements)
        filters_layout.addWidget(self.movement_type_filter)

        filters_layout.addStretch()

        layout.addLayout(filters_layout)

        # Tableau des mouvements
        self.movements_table = QTableWidget()
        self.movements_table.setColumnCount(7)
        self.movements_table.setHorizontalHeaderLabels([
            "Date", "Produit", "Type", "Quantité", "Prix Unit.", "Total", "Fournisseur"
        ])

        # Configuration du tableau
        header = self.movements_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # Produit

        self.movements_table.setAlternatingRowColors(True)
        self.movements_table.setSortingEnabled(True)

        layout.addWidget(self.movements_table)

        self.tab_widget.addTab(movements_widget, "Mouvements")

    def setup_styles(self):
        """Configure les styles CSS"""
        style = """
        #page_title {
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        #primary_button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: bold;
        }

        #primary_button:hover {
            background-color: #0056b3;
        }

        #secondary_button {
            background-color: #6c757d;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
        }

        #secondary_button:hover {
            background-color: #545b62;
        }

        #total_amount {
            font-size: 18px;
            font-weight: bold;
            color: #28a745;
        }

        #info_label {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #bee5eb;
        }

        #stat_value {
            font-size: 18px;
            font-weight: bold;
            color: #28a745;
        }

        #stat_value_warning {
            font-size: 18px;
            font-weight: bold;
            color: #ffc107;
        }

        #stat_value_danger {
            font-size: 18px;
            font-weight: bold;
            color: #dc3545;
        }

        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 5px;
            margin-top: 10px;
            padding-top: 10px;
        }

        QTableWidget {
            gridline-color: #dee2e6;
            background-color: white;
            alternate-background-color: #f8f9fa;
        }
        """
        self.setStyleSheet(style)

    def refresh_data(self):
        """Actualise toutes les données"""
        try:
            # Actualiser l'état du stock
            self.refresh_stock_status()

            # Actualiser les fournisseurs
            self.refresh_suppliers()

            # Actualiser les mouvements
            self.refresh_movements()

            # Charger les produits pour les combos
            self.load_products_for_combos()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'actualisation: {e}")

    def refresh_stock_status(self):
        """Actualise l'état du stock"""
        try:
            self.products = self.product_service.get_all_products()
            self.filter_stock()
            self.update_stock_statistics()

        except Exception as e:
            print(f"Erreur lors de l'actualisation du stock: {e}")

    def filter_stock(self):
        """Filtre l'affichage du stock"""
        if not hasattr(self, 'products'):
            return

        filtered_products = self.products.copy()

        # Filtre par statut
        status_filter = self.status_filter.currentText()
        if status_filter == "Stock bas":
            filtered_products = [p for p in filtered_products if p.is_low_stock() and p.current_stock > 0]
        elif status_filter == "Rupture":
            filtered_products = [p for p in filtered_products if p.current_stock == 0]
        elif status_filter == "En stock":
            filtered_products = [p for p in filtered_products if p.current_stock > p.min_stock_alert]

        # Filtre par recherche
        search_text = self.search_input.text().lower()
        if search_text:
            filtered_products = [p for p in filtered_products if search_text in p.name.lower()]

        # Remplir le tableau
        self.populate_stock_table(filtered_products)

    def populate_stock_table(self, products):
        """Remplit le tableau du stock"""
        self.stock_table.setRowCount(len(products))

        for row, product in enumerate(products):
            # Nom du produit
            self.stock_table.setItem(row, 0, QTableWidgetItem(product.name))

            # Catégorie
            category_display = {"boisson": "Boisson", "plat": "Plat", "snack": "Snack"}
            self.stock_table.setItem(row, 1,
                QTableWidgetItem(category_display.get(product.category, product.category)))

            # Stock actuel
            stock_item = QTableWidgetItem(str(product.current_stock))
            stock_item.setTextAlignment(Qt.AlignCenter)
            if product.current_stock == 0:
                stock_item.setBackground(Qt.red)
                stock_item.setForeground(Qt.white)
            elif product.is_low_stock():
                stock_item.setBackground(Qt.yellow)
            self.stock_table.setItem(row, 2, stock_item)

            # Seuil d'alerte
            threshold_item = QTableWidgetItem(str(product.min_stock_alert))
            threshold_item.setTextAlignment(Qt.AlignCenter)
            self.stock_table.setItem(row, 3, threshold_item)

            # Valeur du stock
            stock_value = product.current_stock * product.purchase_price
            value_item = QTableWidgetItem(f"{stock_value:,.0f} {CURRENCY}")
            value_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.stock_table.setItem(row, 4, value_item)

            # Dernière entrée (à implémenter avec les mouvements)
            self.stock_table.setItem(row, 5, QTableWidgetItem("-"))

            # Statut
            if product.current_stock == 0:
                status = "Rupture"
                status_color = Qt.red
            elif product.is_low_stock():
                status = "Stock bas"
                status_color = Qt.darkYellow
            else:
                status = "En stock"
                status_color = Qt.darkGreen

            status_item = QTableWidgetItem(status)
            status_item.setForeground(status_color)
            status_item.setTextAlignment(Qt.AlignCenter)
            self.stock_table.setItem(row, 6, status_item)

    def update_stock_statistics(self):
        """Met à jour les statistiques du stock"""
        if not hasattr(self, 'products'):
            return

        total_products = len(self.products)
        low_stock_count = len([p for p in self.products if p.is_low_stock() and p.current_stock > 0])
        out_of_stock_count = len([p for p in self.products if p.current_stock == 0])
        total_value = sum(p.current_stock * p.purchase_price for p in self.products)

        self.total_products_label.setText(str(total_products))
        self.low_stock_label.setText(str(low_stock_count))
        self.out_of_stock_label.setText(str(out_of_stock_count))
        self.total_value_label.setText(f"{total_value:,.0f} {CURRENCY}")

    def load_products_for_combos(self):
        """Charge les produits dans les combos"""
        try:
            products = self.product_service.get_all_products()

            if hasattr(self, 'quick_product_combo'):
                self.quick_product_combo.clear()
                for product in products:
                    self.quick_product_combo.addItem(product.name, product)

        except Exception as e:
            print(f"Erreur lors du chargement des produits: {e}")

    def add_stock_entry(self):
        """Ajoute une nouvelle entrée de stock"""
        dialog = StockEntryDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            self.refresh_data()
            QMessageBox.information(self, "Succès", "Entrée de stock enregistrée avec succès!")

    def quick_add_stock(self):
        """Ajoute rapidement du stock"""
        product = self.quick_product_combo.currentData()
        quantity = self.quick_quantity_input.value()

        if not product:
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner un produit.")
            return

        try:
            success = self.product_service.update_stock(
                product_id=product.id,
                quantity_change=quantity,
                movement_type='entree',
                notes="Entrée rapide"
            )

            if success:
                self.refresh_data()
                QMessageBox.information(self, "Succès", f"{quantity} unités ajoutées au stock de {product.name}")
                self.quick_quantity_input.setValue(1)
            else:
                QMessageBox.critical(self, "Erreur", "Erreur lors de l'ajout au stock.")

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur: {e}")

    def add_supplier(self):
        """Ajoute un nouveau fournisseur"""
        dialog = SupplierDialog(parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.refresh_suppliers()
            QMessageBox.information(self, "Succès", "Fournisseur ajouté avec succès!")

    def edit_supplier(self):
        """Modifie le fournisseur sélectionné"""
        current_row = self.suppliers_table.currentRow()
        if current_row >= 0:
            item = self.suppliers_table.item(current_row, 0)
            if item:
                supplier = item.data(Qt.UserRole)
                dialog = SupplierDialog(supplier=supplier, parent=self)
                if dialog.exec_() == QDialog.Accepted:
                    self.refresh_suppliers()
                    QMessageBox.information(self, "Succès", "Fournisseur modifié avec succès!")

    def on_supplier_selection_changed(self):
        """Gère le changement de sélection des fournisseurs"""
        has_selection = len(self.suppliers_table.selectedItems()) > 0
        self.edit_supplier_button.setEnabled(has_selection)

    def refresh_suppliers(self):
        """Actualise la liste des fournisseurs"""
        try:
            session = self.db_manager.get_session()
            suppliers = session.query(Supplier).filter_by(is_active=True).all()

            self.suppliers_table.setRowCount(len(suppliers))

            for row, supplier in enumerate(suppliers):
                # Nom
                name_item = QTableWidgetItem(supplier.name)
                name_item.setData(Qt.UserRole, supplier)
                self.suppliers_table.setItem(row, 0, name_item)

                # Contact
                self.suppliers_table.setItem(row, 1, QTableWidgetItem(supplier.contact_person or "-"))

                # Téléphone
                self.suppliers_table.setItem(row, 2, QTableWidgetItem(supplier.phone or "-"))

                # Email
                self.suppliers_table.setItem(row, 3, QTableWidgetItem(supplier.email or "-"))

                # Dernière livraison (à implémenter)
                self.suppliers_table.setItem(row, 4, QTableWidgetItem("-"))

            session.close()

        except Exception as e:
            print(f"Erreur lors de l'actualisation des fournisseurs: {e}")

    def refresh_movements(self):
        """Actualise les mouvements de stock"""
        # Cette méthode sera implémentée avec les données de mouvements
        pass

    def filter_movements(self):
        """Filtre les mouvements de stock"""
        # Cette méthode sera implémentée avec les données de mouvements
        pass
