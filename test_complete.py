#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de test complet pour l'application Bar-Resto Manager
"""

import sys
import os
from datetime import date, datetime

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """Test des imports principaux"""
    try:
        print("Test des imports...")
        
        # Test des utilitaires
        from utils.config import APP_NAME, UserRole, PERMISSIONS
        print(f"✓ Configuration chargée: {APP_NAME}")
        
        # Test de la base de données
        from models.database import DatabaseManager, User, Product, Sale, Expense
        print("✓ Modèles de base de données importés")
        
        # Test des services
        from models.services import UserService, ProductService, SaleService, ExpenseService
        print("✓ Services importés")
        
        # Test de l'authentification
        from utils.auth import SessionManager
        print("✓ Système d'authentification importé")
        
        # Test des alertes
        from utils.alerts import alert_manager
        print("✓ Système d'alertes importé")
        
        # Test des rapports
        from utils.reports import report_generator
        print("✓ Générateur de rapports importé")
        
        print("\n✅ Tous les imports sont réussis !")
        return True
        
    except ImportError as e:
        print(f"❌ Erreur d'import: {e}")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_database():
    """Test de la base de données"""
    try:
        print("\nTest de la base de données...")
        
        from models.database import DatabaseManager
        
        # Initialiser la base de données
        db_manager = DatabaseManager()
        db_manager.init_database()
        print("✓ Base de données initialisée")
        
        # Test de session
        session = db_manager.get_session()
        session.close()
        print("✓ Session de base de données créée")
        
        print("✅ Base de données fonctionnelle !")
        return True
        
    except Exception as e:
        print(f"❌ Erreur de base de données: {e}")
        return False

def test_auth():
    """Test du système d'authentification"""
    try:
        print("\nTest du système d'authentification...")
        
        from utils.auth import SessionManager
        
        # Test du gestionnaire de session
        session_manager = SessionManager()
        print("✓ Gestionnaire de session créé")
        
        # Test de connexion avec l'admin par défaut
        if session_manager.login('admin', 'admin123'):
            print("✓ Connexion admin réussie")
            
            user = session_manager.get_current_user()
            print(f"✓ Utilisateur connecté: {user.full_name}")
            
            # Test des permissions
            if session_manager.has_permission('manage_users'):
                print("✓ Permissions admin vérifiées")
            
            session_manager.logout()
            print("✓ Déconnexion réussie")
        else:
            print("❌ Échec de la connexion admin")
            return False
        
        print("✅ Système d'authentification fonctionnel !")
        return True
        
    except Exception as e:
        print(f"❌ Erreur d'authentification: {e}")
        return False

def test_services():
    """Test des services de données"""
    try:
        print("\nTest des services de données...")
        
        from models.database import DatabaseManager
        from models.services import ProductService, UserService
        from utils.auth import SessionManager
        
        db_manager = DatabaseManager()
        product_service = ProductService(db_manager)
        user_service = UserService(db_manager)
        session_manager = SessionManager()
        
        # Se connecter comme admin
        session_manager.login('admin', 'admin123')
        
        # Test récupération des produits
        products = product_service.get_all_products()
        print(f"✓ Récupération des produits: {len(products)} produit(s)")
        
        # Test produits en stock bas
        low_stock = product_service.get_low_stock_products()
        print(f"✓ Détection stock bas: {len(low_stock)} produit(s)")
        
        # Test utilisateurs
        users = user_service.get_all_users()
        print(f"✓ Récupération des utilisateurs: {len(users)} utilisateur(s)")
        
        session_manager.logout()
        print("✅ Services de données fonctionnels !")
        return True
        
    except Exception as e:
        print(f"❌ Erreur des services: {e}")
        return False

def test_alerts():
    """Test du système d'alertes"""
    try:
        print("\nTest du système d'alertes...")
        
        from utils.alerts import alert_manager
        
        # Test récupération des alertes
        alerts = alert_manager.get_all_alerts()
        print(f"✓ Récupération des alertes: {len(alerts)} alerte(s)")
        
        # Test résumé des alertes
        summary = alert_manager.get_alerts_summary()
        print(f"✓ Résumé des alertes: {summary['total']} total, {summary['critical']} critiques")
        
        print("✅ Système d'alertes fonctionnel !")
        return True
        
    except Exception as e:
        print(f"❌ Erreur du système d'alertes: {e}")
        return False

def test_reports():
    """Test du système de rapports"""
    try:
        print("\nTest du système de rapports...")
        
        from utils.reports import report_generator
        
        # Test rapport quotidien
        daily_report = report_generator.generate_daily_report(date.today())
        if daily_report:
            print("✓ Génération de rapport quotidien")
        
        # Test rapport de stock
        stock_report = report_generator.generate_stock_report()
        if stock_report:
            print("✓ Génération de rapport de stock")
        
        print("✅ Système de rapports fonctionnel !")
        return True
        
    except Exception as e:
        print(f"❌ Erreur du système de rapports: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🧪 Test complet de l'application Bar-Resto Manager")
    print("=" * 60)
    
    tests = [
        ("Imports", test_imports),
        ("Base de données", test_database),
        ("Authentification", test_auth),
        ("Services", test_services),
        ("Alertes", test_alerts),
        ("Rapports", test_reports)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ Erreur inattendue dans {test_name}: {e}")
            results.append((test_name, False))
    
    # Résumé des résultats
    print("\n" + "=" * 60)
    print("📊 RÉSUMÉ DES TESTS")
    print("=" * 60)
    
    passed = 0
    failed = 0
    
    for test_name, success in results:
        status = "✅ RÉUSSI" if success else "❌ ÉCHOUÉ"
        print(f"{test_name:20} : {status}")
        if success:
            passed += 1
        else:
            failed += 1
    
    print("\n" + "=" * 60)
    print(f"Total: {len(results)} tests")
    print(f"Réussis: {passed}")
    print(f"Échoués: {failed}")
    
    if failed == 0:
        print("\n🎉 TOUS LES TESTS SONT RÉUSSIS !")
        print("\n🚀 L'application est prête à être utilisée !")
        print("\nCommandes disponibles:")
        print("  python main.py                    # Lancer l'application")
        print("  python test_complete.py           # Relancer les tests")
        
        print("\n📋 Informations de connexion par défaut:")
        print("  Utilisateur: admin")
        print("  Mot de passe: admin123")
        
    else:
        print(f"\n⚠️  {failed} test(s) ont échoué.")
        print("Vérifiez les dépendances et les erreurs ci-dessus.")
    
    return 0 if failed == 0 else 1

if __name__ == "__main__":
    sys.exit(main())
