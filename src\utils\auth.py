#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gestionnaire d'authentification et de session - Bar-Resto Manager
"""

from typing import Optional
from datetime import datetime

from models.database import User, DatabaseManager
from models.services import UserService
from utils.config import UserRole, has_permission

class SessionManager:
    """Gestionnaire de session utilisateur"""
    
    _instance = None
    _current_user = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(SessionManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.db_manager = DatabaseManager()
            self.user_service = UserService(self.db_manager)
            self.initialized = True
    
    def login(self, username: str, password: str) -> bool:
        """Connecte un utilisateur"""
        user = self.user_service.authenticate(username, password)
        if user:
            self._current_user = user
            return True
        return False
    
    def logout(self):
        """Déconnecte l'utilisateur actuel"""
        self._current_user = None
    
    def is_logged_in(self) -> bool:
        """Vérifie si un utilisateur est connecté"""
        return self._current_user is not None
    
    def get_current_user(self) -> Optional[User]:
        """Retourne l'utilisateur actuel"""
        return self._current_user
    
    def get_current_user_role(self) -> Optional[UserRole]:
        """Retourne le rôle de l'utilisateur actuel"""
        if self._current_user:
            return UserRole(self._current_user.role)
        return None
    
    def has_permission(self, permission: str) -> bool:
        """Vérifie si l'utilisateur actuel a une permission"""
        if not self._current_user:
            return False
        
        user_role = UserRole(self._current_user.role)
        return has_permission(user_role, permission)
    
    def require_permission(self, permission: str) -> bool:
        """Lève une exception si l'utilisateur n'a pas la permission"""
        if not self.has_permission(permission):
            raise PermissionError(f"Permission '{permission}' requise")
        return True

class AuthDecorator:
    """Décorateur pour vérifier les permissions"""
    
    @staticmethod
    def require_permission(permission: str):
        """Décorateur pour vérifier qu'un utilisateur a une permission"""
        def decorator(func):
            def wrapper(*args, **kwargs):
                session_manager = SessionManager()
                if not session_manager.has_permission(permission):
                    raise PermissionError(f"Permission '{permission}' requise pour cette action")
                return func(*args, **kwargs)
            return wrapper
        return decorator
    
    @staticmethod
    def require_login(func):
        """Décorateur pour vérifier qu'un utilisateur est connecté"""
        def wrapper(*args, **kwargs):
            session_manager = SessionManager()
            if not session_manager.is_logged_in():
                raise PermissionError("Connexion requise pour cette action")
            return func(*args, **kwargs)
        return wrapper

# Fonctions utilitaires pour l'authentification
def get_current_user() -> Optional[User]:
    """Fonction utilitaire pour obtenir l'utilisateur actuel"""
    session_manager = SessionManager()
    return session_manager.get_current_user()

def get_current_user_role() -> Optional[UserRole]:
    """Fonction utilitaire pour obtenir le rôle de l'utilisateur actuel"""
    session_manager = SessionManager()
    return session_manager.get_current_user_role()

def has_current_permission(permission: str) -> bool:
    """Fonction utilitaire pour vérifier une permission"""
    session_manager = SessionManager()
    return session_manager.has_permission(permission)

def require_current_permission(permission: str):
    """Fonction utilitaire pour exiger une permission"""
    session_manager = SessionManager()
    return session_manager.require_permission(permission)
