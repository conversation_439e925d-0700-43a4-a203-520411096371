#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Widget de génération de rapports - Bar-Resto Manager
"""

import os
from datetime import datetime, date, timedelta
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QPushButton, QTableWidget, QTableWidgetItem,
                            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox,
                            QTextEdit, QDialog, QDialogButtonBox, QMessageBox,
                            QHeaderView, QAbstractItemView, QGroupBox, QFormLayout,
                            QDateEdit, QFrame, QTabWidget, QProgressBar,
                            QScrollArea, QTextBrowser)
from PyQt5.QtCore import Qt, pyqtSignal, QDate, QThread, QTimer
from PyQt5.QtGui import QFont, QIcon, QDesktopServices
from PyQt5.QtCore import QUrl

from models.database import DatabaseManager
from utils.auth import SessionManager
from utils.config import CURRENCY, REPORTS_DIR
from utils.reports import report_generator

class ReportGenerationThread(QThread):
    """Thread pour générer les rapports en arrière-plan"""

    progress_updated = pyqtSignal(int)
    report_generated = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)

    def __init__(self, report_type, **kwargs):
        super().__init__()
        self.report_type = report_type
        self.kwargs = kwargs

    def run(self):
        """Génère le rapport"""
        try:
            self.progress_updated.emit(20)

            if self.report_type == "daily":
                target_date = self.kwargs.get('date', date.today())
                data = report_generator.generate_daily_report(target_date)
            elif self.report_type == "monthly":
                year = self.kwargs.get('year', date.today().year)
                month = self.kwargs.get('month', date.today().month)
                data = report_generator.generate_monthly_report(year, month)
            elif self.report_type == "stock":
                data = report_generator.generate_stock_report()
            else:
                raise ValueError(f"Type de rapport non supporté: {self.report_type}")

            self.progress_updated.emit(80)

            if data:
                self.report_generated.emit(data)
            else:
                self.error_occurred.emit("Aucune donnée disponible pour ce rapport")

            self.progress_updated.emit(100)

        except Exception as e:
            self.error_occurred.emit(str(e))

class ReportPreviewDialog(QDialog):
    """Dialog pour prévisualiser un rapport"""

    def __init__(self, report_data, report_type, parent=None):
        super().__init__(parent)
        self.report_data = report_data
        self.report_type = report_type
        self.init_ui()
        self.populate_preview()

    def init_ui(self):
        """Initialise l'interface du dialog"""
        self.setWindowTitle("Aperçu du Rapport")
        self.setMinimumSize(800, 600)

        layout = QVBoxLayout(self)

        # Barre d'outils
        toolbar_layout = QHBoxLayout()

        # Bouton exporter CSV
        export_csv_button = QPushButton("📄 Exporter CSV")
        export_csv_button.clicked.connect(self.export_csv)
        toolbar_layout.addWidget(export_csv_button)

        # Bouton imprimer (simulé)
        print_button = QPushButton("🖨️ Imprimer")
        print_button.clicked.connect(self.print_report)
        toolbar_layout.addWidget(print_button)

        toolbar_layout.addStretch()

        close_button = QPushButton("Fermer")
        close_button.clicked.connect(self.close)
        toolbar_layout.addWidget(close_button)

        layout.addLayout(toolbar_layout)

        # Zone de prévisualisation
        self.preview_browser = QTextBrowser()
        self.preview_browser.setOpenExternalLinks(True)
        layout.addWidget(self.preview_browser)

    def populate_preview(self):
        """Remplit la prévisualisation"""
        if self.report_type == "daily":
            html = self.generate_daily_html()
        elif self.report_type == "monthly":
            html = self.generate_monthly_html()
        elif self.report_type == "stock":
            html = self.generate_stock_html()
        else:
            html = "<h1>Type de rapport non supporté</h1>"

        self.preview_browser.setHtml(html)

    def generate_daily_html(self):
        """Génère le HTML pour un rapport quotidien"""
        data = self.report_data
        date_str = data['date'].strftime('%d/%m/%Y')

        html = f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                h1 {{ color: #2c3e50; border-bottom: 2px solid #3498db; }}
                h2 {{ color: #34495e; }}
                table {{ border-collapse: collapse; width: 100%; margin: 10px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
                .summary {{ background-color: #e8f4fd; padding: 15px; border-radius: 5px; }}
                .positive {{ color: #27ae60; font-weight: bold; }}
                .negative {{ color: #e74c3c; font-weight: bold; }}
            </style>
        </head>
        <body>
            <h1>📊 Rapport Quotidien - {date_str}</h1>

            <div class="summary">
                <h2>Résumé</h2>
                <p><strong>Total des ventes:</strong> <span class="positive">{data['summary']['total_sales']:,.0f} {CURRENCY}</span></p>
                <p><strong>Total des dépenses:</strong> <span class="negative">{data['summary']['total_expenses']:,.0f} {CURRENCY}</span></p>
                <p><strong>Bénéfice:</strong> <span class="{'positive' if data['summary']['profit'] >= 0 else 'negative'}">{data['summary']['profit']:,.0f} {CURRENCY}</span></p>
                <p><strong>Nombre de ventes:</strong> {data['summary']['sales_count']}</p>
                <p><strong>Nombre de dépenses:</strong> {data['summary']['expenses_count']}</p>
            </div>

            <h2>Ventes par Catégorie</h2>
            <table>
                <tr><th>Catégorie</th><th>Montant</th></tr>
        """

        for category, amount in data['sales_by_category'].items():
            category_display = {"boisson": "Boissons", "plat": "Plats", "snack": "Snacks"}.get(category, category)
            html += f"<tr><td>{category_display}</td><td>{amount:,.0f} {CURRENCY}</td></tr>"

        html += """
            </table>

            <h2>Produits les Plus Vendus</h2>
            <table>
                <tr><th>Produit</th><th>Quantité</th><th>Chiffre d'Affaires</th></tr>
        """

        for product_name, stats in data['top_products']:
            html += f"<tr><td>{product_name}</td><td>{stats['quantity']}</td><td>{stats['revenue']:,.0f} {CURRENCY}</td></tr>"

        html += """
            </table>
        </body>
        </html>
        """

        return html

    def generate_monthly_html(self):
        """Génère le HTML pour un rapport mensuel"""
        data = self.report_data

        html = f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                h1 {{ color: #2c3e50; border-bottom: 2px solid #3498db; }}
                h2 {{ color: #34495e; }}
                table {{ border-collapse: collapse; width: 100%; margin: 10px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
                .summary {{ background-color: #e8f4fd; padding: 15px; border-radius: 5px; }}
                .positive {{ color: #27ae60; font-weight: bold; }}
                .negative {{ color: #e74c3c; font-weight: bold; }}
            </style>
        </head>
        <body>
            <h1>📊 Rapport Mensuel - {data['period']}</h1>

            <div class="summary">
                <h2>Résumé du Mois</h2>
                <p><strong>Total des ventes:</strong> <span class="positive">{data['summary']['total_sales']:,.0f} {CURRENCY}</span></p>
                <p><strong>Total des dépenses:</strong> <span class="negative">{data['summary']['total_expenses']:,.0f} {CURRENCY}</span></p>
                <p><strong>Bénéfice total:</strong> <span class="{'positive' if data['summary']['total_profit'] >= 0 else 'negative'}">{data['summary']['total_profit']:,.0f} {CURRENCY}</span></p>
                <p><strong>Moyenne quotidienne des ventes:</strong> {data['summary']['avg_daily_sales']:,.0f} {CURRENCY}</p>
                <p><strong>Nombre total de ventes:</strong> {data['summary']['total_sales_count']}</p>
            </div>

            <h2>Performance Quotidienne</h2>
            <table>
                <tr><th>Date</th><th>Ventes</th><th>Dépenses</th><th>Bénéfice</th></tr>
        """

        for day_data in data['daily_data'][-10:]:  # Afficher les 10 derniers jours
            profit_class = "positive" if day_data['profit'] >= 0 else "negative"
            html += f"""
                <tr>
                    <td>{day_data['date'].strftime('%d/%m/%Y')}</td>
                    <td>{day_data['sales']:,.0f} {CURRENCY}</td>
                    <td>{day_data['expenses']:,.0f} {CURRENCY}</td>
                    <td class="{profit_class}">{day_data['profit']:,.0f} {CURRENCY}</td>
                </tr>
            """

        html += """
            </table>
        </body>
        </html>
        """

        return html

    def generate_stock_html(self):
        """Génère le HTML pour un rapport de stock"""
        data = self.report_data
        date_str = data['date'].strftime('%d/%m/%Y')

        html = f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                h1 {{ color: #2c3e50; border-bottom: 2px solid #3498db; }}
                h2 {{ color: #34495e; }}
                table {{ border-collapse: collapse; width: 100%; margin: 10px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
                .summary {{ background-color: #e8f4fd; padding: 15px; border-radius: 5px; }}
                .warning {{ background-color: #fff3cd; }}
                .danger {{ background-color: #f8d7da; }}
            </style>
        </head>
        <body>
            <h1>📦 Rapport de Stock - {date_str}</h1>

            <div class="summary">
                <h2>Résumé</h2>
                <p><strong>Total produits:</strong> {data['summary']['total_products']}</p>
                <p><strong>Produits en stock bas:</strong> {data['summary']['low_stock_count']}</p>
                <p><strong>Produits en rupture:</strong> {data['summary']['out_of_stock_count']}</p>
                <p><strong>Valeur totale du stock:</strong> {data['summary']['total_stock_value']:,.0f} {CURRENCY}</p>
            </div>

            <h2>Alertes de Stock</h2>
            <table>
                <tr><th>Produit</th><th>Catégorie</th><th>Stock Actuel</th><th>Seuil</th><th>Statut</th></tr>
        """

        # Produits en rupture
        for product in data['out_of_stock_products']:
            html += f"""
                <tr class="danger">
                    <td>{product.name}</td>
                    <td>{product.category}</td>
                    <td>0</td>
                    <td>{product.min_stock_alert}</td>
                    <td>RUPTURE</td>
                </tr>
            """

        # Produits en stock bas
        for product in data['low_stock_products']:
            if product.current_stock > 0:
                html += f"""
                    <tr class="warning">
                        <td>{product.name}</td>
                        <td>{product.category}</td>
                        <td>{product.current_stock}</td>
                        <td>{product.min_stock_alert}</td>
                        <td>STOCK BAS</td>
                    </tr>
                """

        html += """
            </table>
        </body>
        </html>
        """

        return html

    def export_csv(self):
        """Exporte le rapport en CSV"""
        try:
            filepath = report_generator.export_to_csv(self.report_data, self.report_type)
            if filepath:
                QMessageBox.information(
                    self, "Export réussi",
                    f"Rapport exporté vers:\n{filepath}"
                )
                # Ouvrir le dossier contenant le fichier
                QDesktopServices.openUrl(QUrl.fromLocalFile(os.path.dirname(filepath)))
            else:
                QMessageBox.critical(self, "Erreur", "Erreur lors de l'export CSV")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'export: {e}")

    def print_report(self):
        """Simule l'impression du rapport"""
        QMessageBox.information(
            self, "Impression",
            "Fonctionnalité d'impression non implémentée.\n"
            "Utilisez l'export CSV ou copiez le contenu depuis l'aperçu."
        )

class ReportsWidget(QWidget):
    """Widget pour la génération de rapports"""

    def __init__(self):
        super().__init__()
        self.session_manager = SessionManager()
        self.generation_thread = None

        self.init_ui()
        self.setup_styles()
        self.refresh_data()

    def init_ui(self):
        """Initialise l'interface utilisateur"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)

        # Titre
        title_label = QLabel("📊 Génération de Rapports")
        title_label.setObjectName("page_title")
        layout.addWidget(title_label)

        # Onglets
        self.tab_widget = QTabWidget()

        # Onglet Rapports quotidiens
        self.create_daily_reports_tab()

        # Onglet Rapports mensuels
        self.create_monthly_reports_tab()

        # Onglet Rapport de stock
        self.create_stock_reports_tab()

        layout.addWidget(self.tab_widget)

    def create_daily_reports_tab(self):
        """Crée l'onglet des rapports quotidiens"""
        daily_widget = QWidget()
        layout = QVBoxLayout(daily_widget)

        # Configuration
        config_group = QGroupBox("Configuration du Rapport Quotidien")
        config_layout = QFormLayout(config_group)

        # Sélection de la date
        self.daily_date_input = QDateEdit()
        self.daily_date_input.setDate(QDate.currentDate())
        self.daily_date_input.setCalendarPopup(True)
        config_layout.addRow("Date:", self.daily_date_input)

        layout.addWidget(config_group)

        # Boutons d'action
        buttons_layout = QHBoxLayout()

        # Bouton générer
        generate_daily_button = QPushButton("📊 Générer Rapport")
        generate_daily_button.setObjectName("primary_button")
        generate_daily_button.clicked.connect(self.generate_daily_report)
        buttons_layout.addWidget(generate_daily_button)

        # Bouton rapport d'hier
        yesterday_button = QPushButton("📅 Rapport d'Hier")
        yesterday_button.setObjectName("secondary_button")
        yesterday_button.clicked.connect(self.generate_yesterday_report)
        buttons_layout.addWidget(yesterday_button)

        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

        # Barre de progression
        self.daily_progress = QProgressBar()
        self.daily_progress.setVisible(False)
        layout.addWidget(self.daily_progress)

        # Aperçu rapide
        preview_group = QGroupBox("Aperçu Rapide")
        preview_layout = QVBoxLayout(preview_group)

        self.daily_preview_label = QLabel("Sélectionnez une date et générez un rapport pour voir l'aperçu.")
        self.daily_preview_label.setWordWrap(True)
        self.daily_preview_label.setObjectName("preview_text")
        preview_layout.addWidget(self.daily_preview_label)

        layout.addWidget(preview_group)

        layout.addStretch()

        self.tab_widget.addTab(daily_widget, "Rapports Quotidiens")

    def create_monthly_reports_tab(self):
        """Crée l'onglet des rapports mensuels"""
        monthly_widget = QWidget()
        layout = QVBoxLayout(monthly_widget)

        # Configuration
        config_group = QGroupBox("Configuration du Rapport Mensuel")
        config_layout = QFormLayout(config_group)

        # Sélection du mois et de l'année
        month_year_layout = QHBoxLayout()

        self.monthly_month_combo = QComboBox()
        self.monthly_month_combo.addItems([
            "Janvier", "Février", "Mars", "Avril", "Mai", "Juin",
            "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre"
        ])
        self.monthly_month_combo.setCurrentIndex(date.today().month - 1)
        month_year_layout.addWidget(self.monthly_month_combo)

        self.monthly_year_spin = QSpinBox()
        self.monthly_year_spin.setRange(2020, 2030)
        self.monthly_year_spin.setValue(date.today().year)
        month_year_layout.addWidget(self.monthly_year_spin)

        config_layout.addRow("Mois/Année:", month_year_layout)

        layout.addWidget(config_group)

        # Boutons d'action
        buttons_layout = QHBoxLayout()

        # Bouton générer
        generate_monthly_button = QPushButton("📊 Générer Rapport")
        generate_monthly_button.setObjectName("primary_button")
        generate_monthly_button.clicked.connect(self.generate_monthly_report)
        buttons_layout.addWidget(generate_monthly_button)

        # Bouton mois dernier
        last_month_button = QPushButton("📅 Mois Dernier")
        last_month_button.setObjectName("secondary_button")
        last_month_button.clicked.connect(self.generate_last_month_report)
        buttons_layout.addWidget(last_month_button)

        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

        # Barre de progression
        self.monthly_progress = QProgressBar()
        self.monthly_progress.setVisible(False)
        layout.addWidget(self.monthly_progress)

        # Aperçu rapide
        preview_group = QGroupBox("Aperçu Rapide")
        preview_layout = QVBoxLayout(preview_group)

        self.monthly_preview_label = QLabel("Sélectionnez un mois et générez un rapport pour voir l'aperçu.")
        self.monthly_preview_label.setWordWrap(True)
        self.monthly_preview_label.setObjectName("preview_text")
        preview_layout.addWidget(self.monthly_preview_label)

        layout.addWidget(preview_group)

        layout.addStretch()

        self.tab_widget.addTab(monthly_widget, "Rapports Mensuels")

    def create_stock_reports_tab(self):
        """Crée l'onglet du rapport de stock"""
        stock_widget = QWidget()
        layout = QVBoxLayout(stock_widget)

        # Information
        info_label = QLabel("💡 Le rapport de stock affiche l'état actuel de tous les produits.")
        info_label.setObjectName("info_label")
        layout.addWidget(info_label)

        # Boutons d'action
        buttons_layout = QHBoxLayout()

        # Bouton générer
        generate_stock_button = QPushButton("📦 Générer Rapport de Stock")
        generate_stock_button.setObjectName("primary_button")
        generate_stock_button.clicked.connect(self.generate_stock_report)
        buttons_layout.addWidget(generate_stock_button)

        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

        # Barre de progression
        self.stock_progress = QProgressBar()
        self.stock_progress.setVisible(False)
        layout.addWidget(self.stock_progress)

        # Aperçu rapide
        preview_group = QGroupBox("Aperçu Rapide")
        preview_layout = QVBoxLayout(preview_group)

        self.stock_preview_label = QLabel("Générez un rapport de stock pour voir l'aperçu.")
        self.stock_preview_label.setWordWrap(True)
        self.stock_preview_label.setObjectName("preview_text")
        preview_layout.addWidget(self.stock_preview_label)

        layout.addWidget(preview_group)

        layout.addStretch()

        self.tab_widget.addTab(stock_widget, "Rapport de Stock")

    def setup_styles(self):
        """Configure les styles CSS"""
        style = """
        #page_title {
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        #primary_button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            font-weight: bold;
            font-size: 14px;
        }

        #primary_button:hover {
            background-color: #0056b3;
        }

        #secondary_button {
            background-color: #6c757d;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
        }

        #secondary_button:hover {
            background-color: #545b62;
        }

        #info_label {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #bee5eb;
        }

        #preview_text {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            color: #6c757d;
            font-style: italic;
        }

        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 5px;
            margin-top: 10px;
            padding-top: 10px;
        }

        QProgressBar {
            border: 2px solid #cccccc;
            border-radius: 5px;
            text-align: center;
        }

        QProgressBar::chunk {
            background-color: #007bff;
            border-radius: 3px;
        }
        """
        self.setStyleSheet(style)

    def refresh_data(self):
        """Actualise les données"""
        # Rien à actualiser pour les rapports
        pass

    def generate_daily_report(self):
        """Génère un rapport quotidien"""
        target_date = self.daily_date_input.date().toPyDate()
        self.start_report_generation("daily", date=target_date, progress_bar=self.daily_progress)

    def generate_yesterday_report(self):
        """Génère le rapport d'hier"""
        yesterday = date.today() - timedelta(days=1)
        self.daily_date_input.setDate(QDate.fromString(yesterday.strftime("%Y-%m-%d"), "yyyy-MM-dd"))
        self.start_report_generation("daily", date=yesterday, progress_bar=self.daily_progress)

    def generate_monthly_report(self):
        """Génère un rapport mensuel"""
        month = self.monthly_month_combo.currentIndex() + 1
        year = self.monthly_year_spin.value()
        self.start_report_generation("monthly", year=year, month=month, progress_bar=self.monthly_progress)

    def generate_last_month_report(self):
        """Génère le rapport du mois dernier"""
        today = date.today()
        if today.month == 1:
            last_month = 12
            last_year = today.year - 1
        else:
            last_month = today.month - 1
            last_year = today.year

        self.monthly_month_combo.setCurrentIndex(last_month - 1)
        self.monthly_year_spin.setValue(last_year)
        self.start_report_generation("monthly", year=last_year, month=last_month, progress_bar=self.monthly_progress)

    def generate_stock_report(self):
        """Génère un rapport de stock"""
        self.start_report_generation("stock", progress_bar=self.stock_progress)

    def start_report_generation(self, report_type, progress_bar, **kwargs):
        """Démarre la génération d'un rapport en arrière-plan"""
        if self.generation_thread and self.generation_thread.isRunning():
            QMessageBox.warning(self, "Génération en cours", "Un rapport est déjà en cours de génération.")
            return

        # Afficher la barre de progression
        progress_bar.setVisible(True)
        progress_bar.setValue(0)

        # Créer et démarrer le thread
        self.generation_thread = ReportGenerationThread(report_type, **kwargs)
        self.generation_thread.progress_updated.connect(progress_bar.setValue)
        self.generation_thread.report_generated.connect(lambda data: self.on_report_generated(data, report_type, progress_bar))
        self.generation_thread.error_occurred.connect(lambda error: self.on_report_error(error, progress_bar))
        self.generation_thread.start()

    def on_report_generated(self, report_data, report_type, progress_bar):
        """Appelé quand un rapport est généré avec succès"""
        progress_bar.setVisible(False)

        # Mettre à jour l'aperçu
        self.update_preview(report_data, report_type)

        # Afficher le dialog de prévisualisation
        dialog = ReportPreviewDialog(report_data, report_type, self)
        dialog.exec_()

    def on_report_error(self, error_message, progress_bar):
        """Appelé en cas d'erreur lors de la génération"""
        progress_bar.setVisible(False)
        QMessageBox.critical(self, "Erreur", f"Erreur lors de la génération du rapport:\n{error_message}")

    def update_preview(self, report_data, report_type):
        """Met à jour l'aperçu rapide"""
        try:
            if report_type == "daily":
                preview_text = f"""
📊 Rapport du {report_data['date'].strftime('%d/%m/%Y')}

💰 Ventes: {report_data['summary']['total_sales']:,.0f} {CURRENCY}
💸 Dépenses: {report_data['summary']['total_expenses']:,.0f} {CURRENCY}
📈 Bénéfice: {report_data['summary']['profit']:,.0f} {CURRENCY}
🛒 Nombre de ventes: {report_data['summary']['sales_count']}
                """
                self.daily_preview_label.setText(preview_text)

            elif report_type == "monthly":
                preview_text = f"""
📊 Rapport de {report_data['period']}

💰 Total ventes: {report_data['summary']['total_sales']:,.0f} {CURRENCY}
💸 Total dépenses: {report_data['summary']['total_expenses']:,.0f} {CURRENCY}
📈 Bénéfice total: {report_data['summary']['total_profit']:,.0f} {CURRENCY}
📊 Moyenne quotidienne: {report_data['summary']['avg_daily_sales']:,.0f} {CURRENCY}
🛒 Total ventes: {report_data['summary']['total_sales_count']}
                """
                self.monthly_preview_label.setText(preview_text)

            elif report_type == "stock":
                preview_text = f"""
📦 Rapport de Stock du {report_data['date'].strftime('%d/%m/%Y')}

📊 Total produits: {report_data['summary']['total_products']}
⚠️ Stock bas: {report_data['summary']['low_stock_count']}
🔴 Ruptures: {report_data['summary']['out_of_stock_count']}
💰 Valeur totale: {report_data['summary']['total_stock_value']:,.0f} {CURRENCY}
                """
                self.stock_preview_label.setText(preview_text)

        except Exception as e:
            print(f"Erreur lors de la mise à jour de l'aperçu: {e}")
