#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Widget de génération de rapports - Bar-Resto Manager
"""

from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel

class ReportsWidget(QWidget):
    """Widget temporaire pour la génération de rapports"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout(self)
        label = QLabel("📊 Génération de Rapports\n\n(En cours de développement)")
        label.setStyleSheet("font-size: 24px; text-align: center; color: #6c757d;")
        layout.addWidget(label)
    
    def refresh_data(self):
        pass
