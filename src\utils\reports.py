#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Générateur de rapports pour l'application Bar-Resto Manager
"""

import os
from datetime import datetime, date, timedelta
from typing import List, Dict, Any
import tempfile

from models.database import DatabaseManager
from models.services import ProductService, SaleService, ExpenseService
from utils.config import REPORTS_DIR, CURRENCY, COMPANY_INFO, ensure_directories

class ReportGenerator:
    """Générateur de rapports"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.product_service = ProductService(self.db_manager)
        self.sale_service = SaleService(self.db_manager)
        self.expense_service = ExpenseService(self.db_manager)
        ensure_directories()
    
    def generate_daily_report(self, target_date: date = None) -> Dict[str, Any]:
        """Génère un rapport quotidien"""
        if target_date is None:
            target_date = date.today()
        
        try:
            # Données de base
            sales = self.sale_service.get_sales_by_date(target_date)
            expenses = self.expense_service.get_expenses_by_date(target_date)
            
            # Calculs
            total_sales = sum(sale.total_amount for sale in sales)
            total_expenses = sum(expense.amount for expense in expenses)
            profit = total_sales - total_expenses
            
            # Ventes par catégorie
            sales_by_category = {}
            for sale in sales:
                for item in sale.items:
                    category = item.product.category
                    if category not in sales_by_category:
                        sales_by_category[category] = 0
                    sales_by_category[category] += item.total_price
            
            # Dépenses par catégorie
            expenses_by_category = {}
            for expense in expenses:
                category = expense.category
                if category not in expenses_by_category:
                    expenses_by_category[category] = 0
                expenses_by_category[category] += expense.amount
            
            # Produits les plus vendus
            product_sales = {}
            for sale in sales:
                for item in sale.items:
                    product_name = item.product.name
                    if product_name not in product_sales:
                        product_sales[product_name] = {"quantity": 0, "revenue": 0}
                    product_sales[product_name]["quantity"] += item.quantity
                    product_sales[product_name]["revenue"] += item.total_price
            
            # Trier par quantité vendue
            top_products = sorted(
                product_sales.items(),
                key=lambda x: x[1]["quantity"],
                reverse=True
            )[:10]
            
            report_data = {
                "date": target_date,
                "summary": {
                    "total_sales": total_sales,
                    "total_expenses": total_expenses,
                    "profit": profit,
                    "sales_count": len(sales),
                    "expenses_count": len(expenses)
                },
                "sales_by_category": sales_by_category,
                "expenses_by_category": expenses_by_category,
                "top_products": top_products,
                "sales": sales,
                "expenses": expenses
            }
            
            return report_data
            
        except Exception as e:
            print(f"Erreur lors de la génération du rapport quotidien: {e}")
            return {}
    
    def generate_monthly_report(self, year: int, month: int) -> Dict[str, Any]:
        """Génère un rapport mensuel"""
        try:
            # Calculer les dates de début et fin du mois
            start_date = date(year, month, 1)
            if month == 12:
                end_date = date(year + 1, 1, 1) - timedelta(days=1)
            else:
                end_date = date(year, month + 1, 1) - timedelta(days=1)
            
            # Collecter les données jour par jour
            daily_data = []
            current_date = start_date
            
            total_sales = 0
            total_expenses = 0
            total_sales_count = 0
            
            while current_date <= end_date:
                daily_sales = self.sale_service.get_daily_sales_total(current_date)
                daily_expenses_list = self.expense_service.get_expenses_by_date(current_date)
                daily_expenses = sum(expense.amount for expense in daily_expenses_list)
                
                daily_data.append({
                    "date": current_date,
                    "sales": daily_sales,
                    "expenses": daily_expenses,
                    "profit": daily_sales - daily_expenses
                })
                
                total_sales += daily_sales
                total_expenses += daily_expenses
                total_sales_count += len(self.sale_service.get_sales_by_date(current_date))
                
                current_date += timedelta(days=1)
            
            # Statistiques mensuelles
            total_profit = total_sales - total_expenses
            avg_daily_sales = total_sales / len(daily_data) if daily_data else 0
            avg_daily_expenses = total_expenses / len(daily_data) if daily_data else 0
            
            # Meilleurs et pires jours
            best_day = max(daily_data, key=lambda x: x["sales"]) if daily_data else None
            worst_day = min(daily_data, key=lambda x: x["sales"]) if daily_data else None
            
            report_data = {
                "year": year,
                "month": month,
                "period": f"{start_date.strftime('%d/%m/%Y')} - {end_date.strftime('%d/%m/%Y')}",
                "summary": {
                    "total_sales": total_sales,
                    "total_expenses": total_expenses,
                    "total_profit": total_profit,
                    "total_sales_count": total_sales_count,
                    "avg_daily_sales": avg_daily_sales,
                    "avg_daily_expenses": avg_daily_expenses,
                    "working_days": len(daily_data)
                },
                "daily_data": daily_data,
                "best_day": best_day,
                "worst_day": worst_day
            }
            
            return report_data
            
        except Exception as e:
            print(f"Erreur lors de la génération du rapport mensuel: {e}")
            return {}
    
    def generate_stock_report(self) -> Dict[str, Any]:
        """Génère un rapport de stock"""
        try:
            products = self.product_service.get_all_products()
            
            # Statistiques générales
            total_products = len(products)
            low_stock_products = [p for p in products if p.is_low_stock()]
            out_of_stock_products = [p for p in products if p.current_stock == 0]
            
            # Valeur totale du stock
            total_stock_value = sum(p.current_stock * p.purchase_price for p in products)
            
            # Produits par catégorie
            products_by_category = {}
            for product in products:
                category = product.category
                if category not in products_by_category:
                    products_by_category[category] = []
                products_by_category[category].append(product)
            
            report_data = {
                "date": date.today(),
                "summary": {
                    "total_products": total_products,
                    "low_stock_count": len(low_stock_products),
                    "out_of_stock_count": len(out_of_stock_products),
                    "total_stock_value": total_stock_value
                },
                "products": products,
                "low_stock_products": low_stock_products,
                "out_of_stock_products": out_of_stock_products,
                "products_by_category": products_by_category
            }
            
            return report_data
            
        except Exception as e:
            print(f"Erreur lors de la génération du rapport de stock: {e}")
            return {}
    
    def export_to_csv(self, data: Dict[str, Any], report_type: str, filename: str = None) -> str:
        """Exporte les données vers un fichier CSV"""
        try:
            import csv
            
            if filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"{report_type}_{timestamp}.csv"
            
            filepath = os.path.join(REPORTS_DIR, filename)
            
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                if report_type == "daily":
                    self._write_daily_csv(csvfile, data)
                elif report_type == "monthly":
                    self._write_monthly_csv(csvfile, data)
                elif report_type == "stock":
                    self._write_stock_csv(csvfile, data)
            
            return filepath
            
        except Exception as e:
            print(f"Erreur lors de l'export CSV: {e}")
            return ""
    
    def _write_daily_csv(self, csvfile, data):
        """Écrit un rapport quotidien en CSV"""
        writer = csv.writer(csvfile)
        
        # En-tête
        writer.writerow([f"Rapport Quotidien - {data['date'].strftime('%d/%m/%Y')}"])
        writer.writerow([])
        
        # Résumé
        writer.writerow(["RÉSUMÉ"])
        writer.writerow(["Total Ventes", f"{data['summary']['total_sales']:,.0f} {CURRENCY}"])
        writer.writerow(["Total Dépenses", f"{data['summary']['total_expenses']:,.0f} {CURRENCY}"])
        writer.writerow(["Bénéfice", f"{data['summary']['profit']:,.0f} {CURRENCY}"])
        writer.writerow([])
        
        # Ventes
        writer.writerow(["VENTES"])
        writer.writerow(["ID", "Heure", "Table", "Client", "Montant"])
        for sale in data['sales']:
            writer.writerow([
                sale.id,
                sale.created_at.strftime("%H:%M"),
                sale.table_number or "-",
                sale.customer_name or "-",
                f"{sale.total_amount:,.0f}"
            ])
    
    def _write_monthly_csv(self, csvfile, data):
        """Écrit un rapport mensuel en CSV"""
        writer = csv.writer(csvfile)
        
        # En-tête
        writer.writerow([f"Rapport Mensuel - {data['period']}"])
        writer.writerow([])
        
        # Résumé
        writer.writerow(["RÉSUMÉ"])
        writer.writerow(["Total Ventes", f"{data['summary']['total_sales']:,.0f} {CURRENCY}"])
        writer.writerow(["Total Dépenses", f"{data['summary']['total_expenses']:,.0f} {CURRENCY}"])
        writer.writerow(["Bénéfice Total", f"{data['summary']['total_profit']:,.0f} {CURRENCY}"])
        writer.writerow([])
        
        # Données quotidiennes
        writer.writerow(["DONNÉES QUOTIDIENNES"])
        writer.writerow(["Date", "Ventes", "Dépenses", "Bénéfice"])
        for day_data in data['daily_data']:
            writer.writerow([
                day_data['date'].strftime("%d/%m/%Y"),
                f"{day_data['sales']:,.0f}",
                f"{day_data['expenses']:,.0f}",
                f"{day_data['profit']:,.0f}"
            ])
    
    def _write_stock_csv(self, csvfile, data):
        """Écrit un rapport de stock en CSV"""
        writer = csv.writer(csvfile)
        
        # En-tête
        writer.writerow([f"Rapport de Stock - {data['date'].strftime('%d/%m/%Y')}"])
        writer.writerow([])
        
        # Résumé
        writer.writerow(["RÉSUMÉ"])
        writer.writerow(["Total Produits", data['summary']['total_products']])
        writer.writerow(["Stock Bas", data['summary']['low_stock_count']])
        writer.writerow(["Ruptures", data['summary']['out_of_stock_count']])
        writer.writerow(["Valeur Stock", f"{data['summary']['total_stock_value']:,.0f} {CURRENCY}"])
        writer.writerow([])
        
        # Détail des produits
        writer.writerow(["DÉTAIL DES PRODUITS"])
        writer.writerow(["Nom", "Catégorie", "Stock", "Seuil", "Prix Achat", "Valeur"])
        for product in data['products']:
            writer.writerow([
                product.name,
                product.category,
                product.current_stock,
                product.min_stock_alert,
                f"{product.purchase_price:,.0f}",
                f"{product.current_stock * product.purchase_price:,.0f}"
            ])

# Instance globale du générateur de rapports
report_generator = ReportGenerator()
