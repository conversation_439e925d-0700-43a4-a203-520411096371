#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fenêtre principale - Bar-Resto Manager
"""

from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QMenuBar, QMenu, QAction, QToolBar, QStatusBar,
                            QStackedWidget, QLabel, QPushButton, QFrame,
                            QMessageBox, QApplication)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QIcon, QFont, QPixmap

from utils.auth import SessionManager, get_current_user, get_current_user_role
from utils.config import APP_NAME, UserRole, has_permission
from views.dashboard_widget import DashboardWidget
from views.products_widget import ProductsWidget
from views.sales_widget import SalesWidget
from views.stock_widget import StockWidget
from views.expenses_widget import ExpensesWidget
from views.reports_widget import ReportsWidget
from views.users_widget import UsersWidget

class MainWindow(QMainWindow):
    """Fenêtre principale de l'application"""
    
    # Signaux
    logout_requested = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.session_manager = SessionManager()
        self.current_user = get_current_user()
        self.current_role = get_current_user_role()
        
        # Widgets des différentes sections
        self.widgets = {}
        
        self.init_ui()
        self.setup_menu()
        self.setup_toolbar()
        self.setup_statusbar()
        self.setup_styles()
        
        # Timer pour les mises à jour automatiques
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_status)
        self.update_timer.start(30000)  # Mise à jour toutes les 30 secondes
    
    def init_ui(self):
        """Initialise l'interface utilisateur"""
        self.setWindowTitle(f"{APP_NAME} - {self.current_user.full_name} ({self.current_role.value.title()})")
        self.setMinimumSize(1200, 800)
        self.showMaximized()
        
        # Widget central avec pile de widgets
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # Créer la pile de widgets
        self.stacked_widget = QStackedWidget()
        layout.addWidget(self.stacked_widget)
        
        # Initialiser les widgets selon les permissions
        self.init_widgets()
        
        # Afficher le tableau de bord par défaut
        self.show_dashboard()
    
    def init_widgets(self):
        """Initialise les widgets selon les permissions de l'utilisateur"""
        try:
            print("🔧 Initialisation des widgets...")

            # Tableau de bord (toujours accessible)
            print("📊 Création du tableau de bord...")
            self.widgets['dashboard'] = DashboardWidget()
            self.stacked_widget.addWidget(self.widgets['dashboard'])
            print("✅ Tableau de bord créé")

            # Ventes (accessible à tous)
            if self.session_manager.has_permission('make_sales'):
                print("🛒 Création du widget ventes...")
                self.widgets['sales'] = SalesWidget()
                self.stacked_widget.addWidget(self.widgets['sales'])
                print("✅ Widget ventes créé")

            # Gestion des produits
            if self.session_manager.has_permission('manage_products'):
                print("🍽️ Création du widget produits...")
                self.widgets['products'] = ProductsWidget()
                self.stacked_widget.addWidget(self.widgets['products'])
                print("✅ Widget produits créé")

            # Gestion du stock
            if self.session_manager.has_permission('view_stock'):
                print("📦 Création du widget stock...")
                self.widgets['stock'] = StockWidget()
                self.stacked_widget.addWidget(self.widgets['stock'])
                print("✅ Widget stock créé")

            # Gestion des dépenses
            if self.session_manager.has_permission('manage_expenses'):
                print("💸 Création du widget dépenses...")
                self.widgets['expenses'] = ExpensesWidget()
                self.stacked_widget.addWidget(self.widgets['expenses'])
                print("✅ Widget dépenses créé")

            # Rapports
            if self.session_manager.has_permission('generate_reports'):
                print("📊 Création du widget rapports...")
                self.widgets['reports'] = ReportsWidget()
                self.stacked_widget.addWidget(self.widgets['reports'])
                print("✅ Widget rapports créé")

            # Gestion des utilisateurs (Admin seulement)
            if self.session_manager.has_permission('manage_users'):
                print("👥 Création du widget utilisateurs...")
                self.widgets['users'] = UsersWidget()
                self.stacked_widget.addWidget(self.widgets['users'])
                print("✅ Widget utilisateurs créé")

            print(f"✅ {len(self.widgets)} widgets créés avec succès")

        except Exception as e:
            print(f"❌ Erreur lors de l'initialisation des widgets: {e}")
            import traceback
            traceback.print_exc()

            # Créer un widget d'erreur simple
            error_widget = QWidget()
            error_layout = QVBoxLayout(error_widget)
            error_label = QLabel(f"Erreur lors du chargement:\n{str(e)}")
            error_label.setStyleSheet("color: red; font-size: 16px; padding: 20px;")
            error_layout.addWidget(error_label)

            self.widgets['error'] = error_widget
            self.stacked_widget.addWidget(error_widget)
    
    def setup_menu(self):
        """Configure le menu principal"""
        menubar = self.menuBar()
        
        # Menu Fichier
        file_menu = menubar.addMenu('&Fichier')
        
        # Action Déconnexion
        logout_action = QAction('&Déconnexion', self)
        logout_action.setShortcut('Ctrl+L')
        logout_action.setStatusTip('Se déconnecter de l\'application')
        logout_action.triggered.connect(self.logout_requested.emit)
        file_menu.addAction(logout_action)
        
        file_menu.addSeparator()
        
        # Action Quitter
        quit_action = QAction('&Quitter', self)
        quit_action.setShortcut('Ctrl+Q')
        quit_action.setStatusTip('Quitter l\'application')
        quit_action.triggered.connect(self.close)
        file_menu.addAction(quit_action)
        
        # Menu Ventes
        if self.session_manager.has_permission('make_sales'):
            sales_menu = menubar.addMenu('&Ventes')
            
            new_sale_action = QAction('&Nouvelle vente', self)
            new_sale_action.setShortcut('Ctrl+N')
            new_sale_action.triggered.connect(self.show_sales)
            sales_menu.addAction(new_sale_action)
            
            if self.session_manager.has_permission('view_sales_history'):
                sales_history_action = QAction('&Historique des ventes', self)
                sales_history_action.triggered.connect(self.show_sales_history)
                sales_menu.addAction(sales_history_action)
        
        # Menu Gestion
        if (self.session_manager.has_permission('manage_products') or 
            self.session_manager.has_permission('view_stock')):
            
            management_menu = menubar.addMenu('&Gestion')
            
            if self.session_manager.has_permission('manage_products'):
                products_action = QAction('&Produits', self)
                products_action.triggered.connect(self.show_products)
                management_menu.addAction(products_action)
            
            if self.session_manager.has_permission('view_stock'):
                stock_action = QAction('&Stock', self)
                stock_action.triggered.connect(self.show_stock)
                management_menu.addAction(stock_action)
            
            if self.session_manager.has_permission('manage_expenses'):
                expenses_action = QAction('&Dépenses', self)
                expenses_action.triggered.connect(self.show_expenses)
                management_menu.addAction(expenses_action)
        
        # Menu Rapports
        if self.session_manager.has_permission('generate_reports'):
            reports_menu = menubar.addMenu('&Rapports')
            
            daily_report_action = QAction('Rapport &quotidien', self)
            daily_report_action.triggered.connect(self.show_reports)
            reports_menu.addAction(daily_report_action)
            
            monthly_report_action = QAction('Rapport &mensuel', self)
            monthly_report_action.triggered.connect(self.show_reports)
            reports_menu.addAction(monthly_report_action)
        
        # Menu Administration
        if self.session_manager.has_permission('manage_users'):
            admin_menu = menubar.addMenu('&Administration')
            
            users_action = QAction('&Utilisateurs', self)
            users_action.triggered.connect(self.show_users)
            admin_menu.addAction(users_action)
        
        # Menu Aide
        help_menu = menubar.addMenu('&Aide')
        
        about_action = QAction('À &propos', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_toolbar(self):
        """Configure la barre d'outils"""
        toolbar = QToolBar()
        self.addToolBar(toolbar)
        
        # Bouton Tableau de bord
        dashboard_action = QAction('Tableau de bord', self)
        dashboard_action.triggered.connect(self.show_dashboard)
        toolbar.addAction(dashboard_action)
        
        toolbar.addSeparator()
        
        # Bouton Nouvelle vente
        if self.session_manager.has_permission('make_sales'):
            new_sale_action = QAction('Nouvelle vente', self)
            new_sale_action.triggered.connect(self.show_sales)
            toolbar.addAction(new_sale_action)
        
        # Bouton Produits
        if self.session_manager.has_permission('manage_products'):
            products_action = QAction('Produits', self)
            products_action.triggered.connect(self.show_products)
            toolbar.addAction(products_action)
        
        # Bouton Stock
        if self.session_manager.has_permission('view_stock'):
            stock_action = QAction('Stock', self)
            stock_action.triggered.connect(self.show_stock)
            toolbar.addAction(stock_action)
        
        toolbar.addSeparator()
        
        # Bouton Rapports
        if self.session_manager.has_permission('generate_reports'):
            reports_action = QAction('Rapports', self)
            reports_action.triggered.connect(self.show_reports)
            toolbar.addAction(reports_action)
    
    def setup_statusbar(self):
        """Configure la barre de statut"""
        self.statusbar = QStatusBar()
        self.setStatusBar(self.statusbar)
        
        # Informations utilisateur
        user_info = f"Connecté: {self.current_user.full_name} ({self.current_role.value.title()})"
        self.statusbar.showMessage(user_info)
        
        # Widget pour les alertes de stock
        self.stock_alert_label = QLabel()
        self.statusbar.addPermanentWidget(self.stock_alert_label)
        
        # Mettre à jour les alertes
        self.update_status()
    
    def setup_styles(self):
        """Configure les styles CSS"""
        style = """
        QMainWindow {
            background-color: #f8f9fa;
        }
        
        QMenuBar {
            background-color: #343a40;
            color: white;
            border: none;
            padding: 4px;
        }
        
        QMenuBar::item {
            background-color: transparent;
            padding: 8px 12px;
            border-radius: 4px;
        }
        
        QMenuBar::item:selected {
            background-color: #495057;
        }
        
        QMenu {
            background-color: white;
            border: 1px solid #dee2e6;
            border-radius: 4px;
        }
        
        QMenu::item {
            padding: 8px 16px;
        }
        
        QMenu::item:selected {
            background-color: #e9ecef;
        }
        
        QToolBar {
            background-color: #e9ecef;
            border: none;
            spacing: 4px;
            padding: 4px;
        }
        
        QToolBar QAction {
            padding: 8px 12px;
            border-radius: 4px;
        }
        
        QStatusBar {
            background-color: #f8f9fa;
            border-top: 1px solid #dee2e6;
        }
        """
        self.setStyleSheet(style)

    # Méthodes de navigation
    def show_dashboard(self):
        """Affiche le tableau de bord"""
        try:
            print("🏠 Affichage du tableau de bord...")
            if 'dashboard' in self.widgets:
                print("📊 Widget dashboard trouvé, affichage...")
                self.stacked_widget.setCurrentWidget(self.widgets['dashboard'])
                print("✅ Widget dashboard affiché")

                print("🔄 Actualisation des données...")
                self.widgets['dashboard'].refresh_data()
                print("✅ Données actualisées")
            else:
                print("❌ Widget dashboard non trouvé!")
                print(f"Widgets disponibles: {list(self.widgets.keys())}")
        except Exception as e:
            print(f"❌ Erreur lors de l'affichage du dashboard: {e}")
            import traceback
            traceback.print_exc()

    def show_sales(self):
        """Affiche l'interface de vente"""
        if 'sales' in self.widgets:
            self.stacked_widget.setCurrentWidget(self.widgets['sales'])
            self.widgets['sales'].refresh_data()

    def show_sales_history(self):
        """Affiche l'historique des ventes"""
        if 'sales' in self.widgets:
            self.stacked_widget.setCurrentWidget(self.widgets['sales'])
            self.widgets['sales'].show_history()

    def show_products(self):
        """Affiche la gestion des produits"""
        if 'products' in self.widgets:
            self.stacked_widget.setCurrentWidget(self.widgets['products'])
            self.widgets['products'].refresh_data()

    def show_stock(self):
        """Affiche la gestion du stock"""
        if 'stock' in self.widgets:
            self.stacked_widget.setCurrentWidget(self.widgets['stock'])
            self.widgets['stock'].refresh_data()

    def show_expenses(self):
        """Affiche la gestion des dépenses"""
        if 'expenses' in self.widgets:
            self.stacked_widget.setCurrentWidget(self.widgets['expenses'])
            self.widgets['expenses'].refresh_data()

    def show_reports(self):
        """Affiche les rapports"""
        if 'reports' in self.widgets:
            self.stacked_widget.setCurrentWidget(self.widgets['reports'])
            self.widgets['reports'].refresh_data()

    def show_users(self):
        """Affiche la gestion des utilisateurs"""
        if 'users' in self.widgets:
            self.stacked_widget.setCurrentWidget(self.widgets['users'])
            self.widgets['users'].refresh_data()

    def update_status(self):
        """Met à jour la barre de statut avec les alertes"""
        try:
            from models.database import DatabaseManager
            from models.services import ProductService

            db_manager = DatabaseManager()
            product_service = ProductService(db_manager)

            # Vérifier les produits en stock bas
            low_stock_products = product_service.get_low_stock_products()

            if low_stock_products:
                alert_text = f"⚠️ {len(low_stock_products)} produit(s) en stock bas"
                self.stock_alert_label.setText(alert_text)
                self.stock_alert_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
            else:
                self.stock_alert_label.setText("✅ Stock OK")
                self.stock_alert_label.setStyleSheet("color: #27ae60; font-weight: bold;")

        except Exception as e:
            print(f"Erreur lors de la mise à jour du statut: {e}")

    def show_about(self):
        """Affiche la boîte de dialogue À propos"""
        about_text = f"""
        <h2>{APP_NAME}</h2>
        <p><b>Version:</b> 1.0.0</p>
        <p><b>Description:</b> Système de gestion de stock pour bar-restaurant</p>
        <p><b>Développé pour:</b> Établissements au Burundi</p>
        <p><b>Technologies:</b> Python, PyQt5, SQLite</p>
        <p><b>Utilisateur connecté:</b> {self.current_user.full_name}</p>
        <p><b>Rôle:</b> {self.current_role.value.title()}</p>
        """

        QMessageBox.about(self, "À propos", about_text)

    def closeEvent(self, event):
        """Gère la fermeture de la fenêtre"""
        reply = QMessageBox.question(
            self,
            "Fermer l'application",
            "Êtes-vous sûr de vouloir fermer l'application ?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Arrêter le timer
            if hasattr(self, 'update_timer'):
                self.update_timer.stop()
            event.accept()
        else:
            event.ignore()
