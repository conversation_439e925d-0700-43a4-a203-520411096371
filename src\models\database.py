#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gestionnaire de base de données pour l'application Bar-Resto Manager
"""

import os
from datetime import datetime
from sqlalchemy import create_engine, Column, Integer, String, Float, DateTime, Boolean, ForeignKey, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.sql import func
import bcrypt

from utils.config import DATABASE_PATH, UserRole, ProductCategory, OrderStatus, ensure_directories

Base = declarative_base()

class User(Base):
    """Modèle pour les utilisateurs"""
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True)
    username = Column(String(50), unique=True, nullable=False)
    password_hash = Column(String(128), nullable=False)
    full_name = Column(String(100), nullable=False)
    role = Column(String(20), nullable=False)  # admin, gerant, serveur
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    last_login = Column(DateTime)
    
    # Relations
    sales = relationship("Sale", back_populates="user")
    expenses = relationship("Expense", back_populates="user")
    
    def set_password(self, password):
        """Hash et stocke le mot de passe"""
        self.password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
    
    def check_password(self, password):
        """Vérifie le mot de passe"""
        return bcrypt.checkpw(password.encode('utf-8'), self.password_hash.encode('utf-8'))
    
    def get_role(self):
        """Retourne le rôle sous forme d'enum"""
        return UserRole(self.role)

class Product(Base):
    """Modèle pour les produits"""
    __tablename__ = 'products'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    category = Column(String(20), nullable=False)  # boisson, plat, snack
    purchase_price = Column(Float, nullable=False)
    selling_price = Column(Float, nullable=False)
    current_stock = Column(Integer, default=0)
    min_stock_alert = Column(Integer, default=5)
    description = Column(Text)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relations
    sale_items = relationship("SaleItem", back_populates="product")
    stock_movements = relationship("StockMovement", back_populates="product")
    
    def get_category(self):
        """Retourne la catégorie sous forme d'enum"""
        return ProductCategory(self.category)
    
    def is_low_stock(self):
        """Vérifie si le stock est bas"""
        return self.current_stock <= self.min_stock_alert

class Supplier(Base):
    """Modèle pour les fournisseurs"""
    __tablename__ = 'suppliers'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    contact_person = Column(String(100))
    phone = Column(String(20))
    email = Column(String(100))
    address = Column(Text)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relations
    stock_movements = relationship("StockMovement", back_populates="supplier")

class StockMovement(Base):
    """Modèle pour les mouvements de stock"""
    __tablename__ = 'stock_movements'
    
    id = Column(Integer, primary_key=True)
    product_id = Column(Integer, ForeignKey('products.id'), nullable=False)
    supplier_id = Column(Integer, ForeignKey('suppliers.id'))
    movement_type = Column(String(20), nullable=False)  # entree, sortie
    quantity = Column(Integer, nullable=False)
    unit_price = Column(Float)
    total_cost = Column(Float)
    notes = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relations
    product = relationship("Product", back_populates="stock_movements")
    supplier = relationship("Supplier", back_populates="stock_movements")

class Sale(Base):
    """Modèle pour les ventes"""
    __tablename__ = 'sales'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    table_number = Column(String(10))
    customer_name = Column(String(100))
    total_amount = Column(Float, nullable=False)
    payment_method = Column(String(20), default='cash')  # cash, card, mobile
    status = Column(String(20), default='terminee')
    notes = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relations
    user = relationship("User", back_populates="sales")
    items = relationship("SaleItem", back_populates="sale", cascade="all, delete-orphan")

class SaleItem(Base):
    """Modèle pour les articles d'une vente"""
    __tablename__ = 'sale_items'
    
    id = Column(Integer, primary_key=True)
    sale_id = Column(Integer, ForeignKey('sales.id'), nullable=False)
    product_id = Column(Integer, ForeignKey('products.id'), nullable=False)
    quantity = Column(Integer, nullable=False)
    unit_price = Column(Float, nullable=False)
    total_price = Column(Float, nullable=False)
    
    # Relations
    sale = relationship("Sale", back_populates="items")
    product = relationship("Product", back_populates="sale_items")

class Expense(Base):
    """Modèle pour les dépenses"""
    __tablename__ = 'expenses'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    category = Column(String(50), nullable=False)  # gaz, salaire, electricite, etc.
    description = Column(String(200), nullable=False)
    amount = Column(Float, nullable=False)
    payment_method = Column(String(20), default='cash')
    receipt_number = Column(String(50))
    notes = Column(Text)
    expense_date = Column(DateTime, default=datetime.utcnow)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relations
    user = relationship("User", back_populates="expenses")

class DatabaseManager:
    """Gestionnaire de base de données"""
    
    def __init__(self):
        ensure_directories()
        self.engine = create_engine(f'sqlite:///{DATABASE_PATH}', echo=False)
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
    
    def init_database(self):
        """Initialise la base de données et crée les tables"""
        Base.metadata.create_all(bind=self.engine)
        self._create_default_admin()
    
    def get_session(self):
        """Retourne une nouvelle session de base de données"""
        return self.SessionLocal()
    
    def _create_default_admin(self):
        """Crée un utilisateur admin par défaut s'il n'existe pas"""
        session = self.get_session()
        try:
            admin_exists = session.query(User).filter_by(username='admin').first()
            if not admin_exists:
                admin = User(
                    username='admin',
                    full_name='Administrateur',
                    role='admin'
                )
                admin.set_password('admin123')  # Mot de passe par défaut
                session.add(admin)
                session.commit()
                print("Utilisateur admin créé avec le mot de passe: admin123")
        except Exception as e:
            session.rollback()
            print(f"Erreur lors de la création de l'admin: {e}")
        finally:
            session.close()
