#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Contrôleur principal de l'application Bar-Resto Manager
"""

from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import QObject, pyqtSignal

from views.login_window import LoginWindow
from views.main_window import MainWindow
from utils.auth import SessionManager
from utils.config import UserRole

class MainController(QObject):
    """Contrôleur principal de l'application"""
    
    # Signaux
    user_logged_in = pyqtSignal(object)  # Émis quand un utilisateur se connecte
    user_logged_out = pyqtSignal()       # Émis quand un utilisateur se déconnecte
    
    def __init__(self):
        super().__init__()
        self.session_manager = SessionManager()
        self.login_window = None
        self.main_window = None
        
        # Connecter les signaux
        self.user_logged_in.connect(self._on_user_logged_in)
        self.user_logged_out.connect(self._on_user_logged_out)
    
    def show_login(self):
        """Affiche la fenêtre de connexion"""
        if self.main_window:
            self.main_window.close()
            self.main_window = None
        
        self.login_window = LoginWindow()
        self.login_window.login_successful.connect(self._handle_login_success)
        self.login_window.show()
    
    def show_main_window(self):
        """Affiche la fenêtre principale"""
        if not self.session_manager.is_logged_in():
            self.show_login()
            return
        
        if self.login_window:
            self.login_window.close()
            self.login_window = None
        
        self.main_window = MainWindow()
        self.main_window.logout_requested.connect(self._handle_logout)
        self.main_window.show()
    
    def _handle_login_success(self, username: str, password: str):
        """Gère une tentative de connexion réussie"""
        if self.session_manager.login(username, password):
            user = self.session_manager.get_current_user()
            self.user_logged_in.emit(user)
            self.show_main_window()
        else:
            QMessageBox.warning(
                self.login_window,
                "Erreur de connexion",
                "Nom d'utilisateur ou mot de passe incorrect."
            )
    
    def _handle_logout(self):
        """Gère la déconnexion"""
        reply = QMessageBox.question(
            self.main_window,
            "Déconnexion",
            "Êtes-vous sûr de vouloir vous déconnecter ?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.session_manager.logout()
            self.user_logged_out.emit()
            self.show_login()
    
    def _on_user_logged_in(self, user):
        """Appelé quand un utilisateur se connecte"""
        print(f"Utilisateur connecté: {user.full_name} ({user.role})")
    
    def _on_user_logged_out(self):
        """Appelé quand un utilisateur se déconnecte"""
        print("Utilisateur déconnecté")
    
    def quit_application(self):
        """Ferme l'application"""
        if self.main_window:
            self.main_window.close()
        if self.login_window:
            self.login_window.close()
        QApplication.quit()
