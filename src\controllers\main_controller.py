#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Contrôleur principal de l'application Bar-Resto Manager
"""

from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import QObject, pyqtSignal

from views.login_window import LoginWindow
from views.main_window import MainWindow
from utils.auth import SessionManager
from utils.config import UserRole

class MainController(QObject):
    """Contrôleur principal de l'application"""
    
    # Signaux
    user_logged_in = pyqtSignal(object)  # Émis quand un utilisateur se connecte
    user_logged_out = pyqtSignal()       # Émis quand un utilisateur se déconnecte
    
    def __init__(self):
        super().__init__()
        self.session_manager = SessionManager()
        self.login_window = None
        self.main_window = None
        
        # Connecter les signaux
        self.user_logged_in.connect(self._on_user_logged_in)
        self.user_logged_out.connect(self._on_user_logged_out)
    
    def show_login(self):
        """Affiche la fenêtre de connexion"""
        if self.main_window:
            self.main_window.close()
            self.main_window = None
        
        self.login_window = LoginWindow()
        self.login_window.login_successful.connect(self._handle_login_success)
        self.login_window.show()
    
    def show_main_window(self):
        """Affiche la fenêtre principale"""
        try:
            print("🏠 Début de show_main_window...")

            if not self.session_manager.is_logged_in():
                print("❌ Utilisateur non connecté, retour à la connexion")
                self.show_login()
                return

            print("✅ Utilisateur connecté, création de la fenêtre principale...")

            if self.login_window:
                print("🔐 Fermeture de la fenêtre de connexion...")
                self.login_window.close()
                self.login_window = None

            print("🏗️ Création de MainWindow...")
            self.main_window = MainWindow()

            print("🔗 Connexion du signal logout...")
            self.main_window.logout_requested.connect(self._handle_logout)

            print("👁️ Affichage de la fenêtre principale...")
            self.main_window.show()

            print("✅ Fenêtre principale affichée avec succès!")

        except Exception as e:
            print(f"❌ Erreur lors de l'affichage de la fenêtre principale: {e}")
            import traceback
            traceback.print_exc()
    
    def _handle_login_success(self, username: str, password: str):
        """Gère une tentative de connexion réussie"""
        print(f"🔐 Tentative de connexion: {username}")

        if self.session_manager.login(username, password):
            user = self.session_manager.get_current_user()
            print(f"✅ Connexion réussie pour: {username}")

            # Émettre le signal (pour les logs)
            self.user_logged_in.emit(user)

            # Afficher la fenêtre principale
            self.show_main_window()
        else:
            print(f"❌ Échec de connexion pour: {username}")
            QMessageBox.warning(
                self.login_window,
                "Erreur de connexion",
                "Nom d'utilisateur ou mot de passe incorrect."
            )
    
    def _handle_logout(self):
        """Gère la déconnexion"""
        reply = QMessageBox.question(
            self.main_window,
            "Déconnexion",
            "Êtes-vous sûr de vouloir vous déconnecter ?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.session_manager.logout()
            self.user_logged_out.emit()
            self.show_login()
    
    def _on_user_logged_in(self, user):
        """Appelé quand un utilisateur se connecte (pour logging seulement)"""
        try:
            # Récupérer les informations utilisateur de manière sûre
            full_name = getattr(user, 'full_name', 'Nom inconnu')
            role = getattr(user, 'role', 'Rôle inconnu')
            print(f"📝 Log: Utilisateur connecté: {full_name} ({role})")

        except Exception as e:
            print(f"❌ Erreur lors du logging de connexion: {e}")
    
    def _on_user_logged_out(self):
        """Appelé quand un utilisateur se déconnecte"""
        print("Utilisateur déconnecté")
    
    def quit_application(self):
        """Ferme l'application"""
        if self.main_window:
            self.main_window.close()
        if self.login_window:
            self.login_window.close()
        QApplication.quit()
