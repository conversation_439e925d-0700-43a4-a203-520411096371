#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test simple pour vérifier que l'application fonctionne
"""

import sys
import os

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_basic_imports():
    """Test des imports de base"""
    try:
        print("🧪 Test des imports de base...")
        
        # Test configuration
        from utils.config import APP_NAME, UserRole
        print(f"✅ Configuration OK: {APP_NAME}")
        
        # Test base de données
        from models.database import DatabaseManager
        print("✅ Base de données OK")
        
        # Test authentification
        from utils.auth import SessionManager
        print("✅ Authentification OK")
        
        return True
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_database_init():
    """Test d'initialisation de la base de données"""
    try:
        print("\n🧪 Test d'initialisation de la base de données...")
        
        from models.database import DatabaseManager
        
        db_manager = DatabaseManager()
        db_manager.init_database()
        print("✅ Base de données initialisée")
        
        return True
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_auth_system():
    """Test du système d'authentification"""
    try:
        print("\n🧪 Test du système d'authentification...")
        
        from utils.auth import SessionManager
        
        session_manager = SessionManager()
        print("✅ SessionManager créé")
        
        # Test de connexion admin
        if session_manager.login('admin', 'admin123'):
            print("✅ Connexion admin réussie")
            
            user = session_manager.get_current_user()
            if user:
                print(f"✅ Utilisateur récupéré: {user.username}")
            
            session_manager.logout()
            print("✅ Déconnexion réussie")
            
            return True
        else:
            print("❌ Échec de la connexion admin")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def main():
    """Fonction principale"""
    print("🚀 Test Simple - Bar-Resto Manager")
    print("=" * 50)
    
    tests = [
        ("Imports de base", test_basic_imports),
        ("Initialisation BDD", test_database_init),
        ("Système d'auth", test_auth_system)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: RÉUSSI")
            else:
                failed += 1
                print(f"❌ {test_name}: ÉCHOUÉ")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name}: ERREUR - {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Résultats: {passed} réussis, {failed} échoués")
    
    if failed == 0:
        print("\n🎉 TOUS LES TESTS SONT RÉUSSIS !")
        print("\n🚀 L'application peut être lancée avec:")
        print("   python main.py")
        print("\n📋 Connexion par défaut:")
        print("   Utilisateur: admin")
        print("   Mot de passe: admin123")
    else:
        print(f"\n⚠️ {failed} test(s) ont échoué")
    
    return 0 if failed == 0 else 1

if __name__ == "__main__":
    sys.exit(main())
