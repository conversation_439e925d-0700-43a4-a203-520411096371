#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Configuration de l'application Bar-Resto Manager
"""

import os
from enum import Enum

# Chemins de l'application
BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
DATA_DIR = os.path.join(BASE_DIR, 'data')
REPORTS_DIR = os.path.join(BASE_DIR, 'reports')
RESOURCES_DIR = os.path.join(BASE_DIR, 'resources')

# Base de données
DATABASE_PATH = os.path.join(DATA_DIR, 'bar_resto.db')

# Configuration de l'application
APP_NAME = "Bar-Resto Manager"
APP_VERSION = "1.0.0"
COMPANY_NAME = "BarResto Solutions"

# Rôles utilisateur
class UserRole(Enum):
    ADMIN = "admin"
    GERANT = "gerant"
    SERVEUR = "serveur"

# Catégories de produits
class ProductCategory(Enum):
    BOISSON = "boisson"
    PLAT = "plat"
    SNACK = "snack"

# Statuts de commande
class OrderStatus(Enum):
    EN_COURS = "en_cours"
    TERMINEE = "terminee"
    ANNULEE = "annulee"

# Configuration des alertes de stock
STOCK_ALERT_THRESHOLD = 5  # Alerte quand stock <= 5

# Configuration des rapports
CURRENCY = "BIF"  # Franc Burundais
COMPANY_INFO = {
    "name": "Bar-Restaurant",
    "address": "Bujumbura, Burundi",
    "phone": "+257 XX XX XX XX",
    "email": "<EMAIL>"
}

# Permissions par rôle
PERMISSIONS = {
    UserRole.ADMIN: {
        'login': True,
        'manage_users': True,
        'manage_products': True,
        'make_sales': True,
        'view_sales_history': True,
        'manage_stock': True,
        'view_stock': True,
        'generate_reports': True,
        'manage_expenses': True,
        'delete_operations': True,
        'manage_database': True
    },
    UserRole.GERANT: {
        'login': True,
        'manage_users': False,
        'manage_products': True,
        'make_sales': True,
        'view_sales_history': True,
        'manage_stock': True,
        'view_stock': True,
        'generate_reports': True,
        'manage_expenses': True,
        'delete_operations': False,
        'manage_database': False
    },
    UserRole.SERVEUR: {
        'login': True,
        'manage_users': False,
        'manage_products': False,
        'make_sales': True,
        'view_sales_history': False,
        'manage_stock': False,
        'view_stock': False,
        'generate_reports': False,
        'manage_expenses': False,
        'delete_operations': False,
        'manage_database': False
    }
}

def has_permission(user_role: UserRole, permission: str) -> bool:
    """Vérifie si un rôle a une permission donnée"""
    return PERMISSIONS.get(user_role, {}).get(permission, False)

def ensure_directories():
    """Crée les répertoires nécessaires s'ils n'existent pas"""
    for directory in [DATA_DIR, REPORTS_DIR, RESOURCES_DIR]:
        os.makedirs(directory, exist_ok=True)
