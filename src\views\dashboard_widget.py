#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Widget du tableau de bord - Bar-Resto Manager
"""

from datetime import datetime, date
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QFrame, QPushButton, QTableWidget, 
                            QTableWidgetItem, QHeaderView, QScrollArea)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QPalette

from models.database import DatabaseManager
from models.services import ProductService, SaleService, ExpenseService
from utils.auth import get_current_user, SessionManager
from utils.config import CURRENCY
from utils.alerts import alert_manager, AlertLevel

class DashboardWidget(QWidget):
    """Widget du tableau de bord principal"""
    
    def __init__(self):
        super().__init__()
        try:
            print("🏗️ Initialisation du DashboardWidget...")

            self.db_manager = DatabaseManager()
            self.product_service = ProductService(self.db_manager)
            self.sale_service = SaleService(self.db_manager)
            self.expense_service = ExpenseService(self.db_manager)
            self.session_manager = SessionManager()
            print("✅ Services initialisés")

            self.init_ui()
            print("✅ Interface créée")

            self.setup_styles()
            print("✅ Styles appliqués")

            # Timer pour actualisation automatique
            self.refresh_timer = QTimer()
            self.refresh_timer.timeout.connect(self.refresh_data)
            self.refresh_timer.start(60000)  # Actualiser chaque minute
            print("✅ Timer configuré")

            # Actualiser les données en dernier
            self.refresh_data()
            print("✅ DashboardWidget initialisé avec succès")

        except Exception as e:
            print(f"❌ Erreur lors de l'initialisation du DashboardWidget: {e}")
            import traceback
            traceback.print_exc()

            # Créer une interface d'erreur minimale
            self.create_error_ui(str(e))

    def create_error_ui(self, error_message):
        """Crée une interface d'erreur simple"""
        layout = QVBoxLayout(self)

        error_label = QLabel(f"❌ Erreur lors du chargement du tableau de bord:\n\n{error_message}")
        error_label.setStyleSheet("""
            color: #dc3545;
            font-size: 16px;
            padding: 20px;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
        """)
        error_label.setWordWrap(True)
        layout.addWidget(error_label)

        retry_button = QPushButton("🔄 Réessayer")
        retry_button.clicked.connect(self.retry_initialization)
        layout.addWidget(retry_button)

    def retry_initialization(self):
        """Réessaie l'initialisation"""
        try:
            # Nettoyer le layout existant
            for i in reversed(range(self.layout().count())):
                self.layout().itemAt(i).widget().setParent(None)

            # Réessayer l'initialisation
            self.__init__()
        except Exception as e:
            print(f"Erreur lors de la réinitialisation: {e}")
    
    def init_ui(self):
        """Initialise l'interface utilisateur"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Titre du tableau de bord
        title_label = QLabel("📊 Tableau de Bord")
        title_label.setObjectName("dashboard_title")
        layout.addWidget(title_label)
        
        # Scroll area pour le contenu
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        
        # Cartes de statistiques
        self.create_stats_cards(content_layout)
        
        # Section des alertes et informations
        self.create_alerts_section(content_layout)
        
        # Section des ventes récentes (si autorisé)
        if self.session_manager.has_permission('view_sales_history'):
            self.create_recent_sales_section(content_layout)
        
        scroll_area.setWidget(content_widget)
        layout.addWidget(scroll_area)
    
    def create_stats_cards(self, layout):
        """Crée les cartes de statistiques"""
        cards_layout = QGridLayout()
        cards_layout.setSpacing(15)
        
        # Carte des ventes du jour
        self.daily_sales_card = self.create_stat_card(
            "💰 Ventes du Jour", "0 BIF", "#28a745"
        )
        cards_layout.addWidget(self.daily_sales_card, 0, 0)
        
        # Carte du nombre de produits
        self.products_count_card = self.create_stat_card(
            "📦 Produits", "0", "#17a2b8"
        )
        cards_layout.addWidget(self.products_count_card, 0, 1)
        
        # Carte des alertes de stock
        self.stock_alerts_card = self.create_stat_card(
            "⚠️ Alertes Stock", "0", "#dc3545"
        )
        cards_layout.addWidget(self.stock_alerts_card, 0, 2)
        
        # Carte des dépenses du jour (si autorisé)
        if self.session_manager.has_permission('manage_expenses'):
            self.daily_expenses_card = self.create_stat_card(
                "💸 Dépenses du Jour", "0 BIF", "#fd7e14"
            )
            cards_layout.addWidget(self.daily_expenses_card, 1, 0)
        
        # Carte du bénéfice estimé du jour
        self.daily_profit_card = self.create_stat_card(
            "📈 Bénéfice Estimé", "0 BIF", "#6f42c1"
        )
        cards_layout.addWidget(self.daily_profit_card, 1, 1)
        
        layout.addLayout(cards_layout)
    
    def create_stat_card(self, title, value, color):
        """Crée une carte de statistique"""
        card = QFrame()
        card.setObjectName("stat_card")
        card.setFrameShape(QFrame.Box)
        card.setFixedHeight(120)
        
        card_layout = QVBoxLayout(card)
        card_layout.setAlignment(Qt.AlignCenter)
        
        # Titre
        title_label = QLabel(title)
        title_label.setObjectName("card_title")
        title_label.setAlignment(Qt.AlignCenter)
        card_layout.addWidget(title_label)
        
        # Valeur
        value_label = QLabel(value)
        value_label.setObjectName("card_value")
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet(f"color: {color}; font-size: 24px; font-weight: bold;")
        card_layout.addWidget(value_label)
        
        return card
    
    def create_alerts_section(self, layout):
        """Crée la section des alertes"""
        alerts_frame = QFrame()
        alerts_frame.setObjectName("alerts_frame")
        alerts_frame.setFrameShape(QFrame.Box)
        
        alerts_layout = QVBoxLayout(alerts_frame)
        
        # Titre
        alerts_title = QLabel("🚨 Alertes et Notifications")
        alerts_title.setObjectName("section_title")
        alerts_layout.addWidget(alerts_title)
        
        # Liste des alertes
        self.alerts_label = QLabel("Aucune alerte pour le moment")
        self.alerts_label.setObjectName("alerts_content")
        self.alerts_label.setWordWrap(True)
        alerts_layout.addWidget(self.alerts_label)
        
        layout.addWidget(alerts_frame)
    
    def create_recent_sales_section(self, layout):
        """Crée la section des ventes récentes"""
        sales_frame = QFrame()
        sales_frame.setObjectName("sales_frame")
        sales_frame.setFrameShape(QFrame.Box)
        
        sales_layout = QVBoxLayout(sales_frame)
        
        # Titre
        sales_title = QLabel("🛒 Ventes Récentes")
        sales_title.setObjectName("section_title")
        sales_layout.addWidget(sales_title)
        
        # Tableau des ventes récentes
        self.recent_sales_table = QTableWidget()
        self.recent_sales_table.setColumnCount(4)
        self.recent_sales_table.setHorizontalHeaderLabels([
            "Heure", "Table", "Client", "Montant"
        ])
        self.recent_sales_table.horizontalHeader().setStretchLastSection(True)
        self.recent_sales_table.setMaximumHeight(200)
        self.recent_sales_table.setAlternatingRowColors(True)
        sales_layout.addWidget(self.recent_sales_table)
        
        layout.addWidget(sales_frame)
    
    def setup_styles(self):
        """Configure les styles CSS"""
        style = """
        #dashboard_title {
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        #stat_card {
            background-color: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 10px;
        }
        
        #stat_card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        #card_title {
            font-size: 14px;
            color: #6c757d;
            font-weight: bold;
        }
        
        #card_value {
            font-size: 24px;
            font-weight: bold;
            margin-top: 5px;
        }
        
        #alerts_frame, #sales_frame {
            background-color: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-top: 10px;
        }
        
        #section_title {
            font-size: 18px;
            font-weight: bold;
            color: #495057;
            margin-bottom: 10px;
        }
        
        #alerts_content {
            font-size: 14px;
            color: #6c757d;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        
        QTableWidget {
            gridline-color: #dee2e6;
            background-color: white;
            alternate-background-color: #f8f9fa;
        }
        
        QTableWidget::item {
            padding: 8px;
        }
        
        QHeaderView::section {
            background-color: #e9ecef;
            padding: 8px;
            border: none;
            font-weight: bold;
        }
        """
        self.setStyleSheet(style)
    
    def refresh_data(self):
        """Actualise toutes les données du tableau de bord"""
        try:
            print("🔄 Actualisation des données du tableau de bord...")
            today = date.today()

            # Ventes du jour
            print("💰 Récupération des ventes du jour...")
            daily_sales = self.sale_service.get_daily_sales_total(today)
            if hasattr(self, 'daily_sales_card'):
                self.update_card_value(self.daily_sales_card, f"{daily_sales:,.0f} {CURRENCY}")
                print(f"✅ Ventes du jour: {daily_sales:,.0f} {CURRENCY}")

            # Nombre de produits
            print("📦 Récupération des produits...")
            products = self.product_service.get_all_products()
            if hasattr(self, 'products_count_card'):
                self.update_card_value(self.products_count_card, str(len(products)))
                print(f"✅ Nombre de produits: {len(products)}")

            # Alertes de stock
            print("⚠️ Vérification des alertes de stock...")
            low_stock_products = self.product_service.get_low_stock_products()
            if hasattr(self, 'stock_alerts_card'):
                self.update_card_value(self.stock_alerts_card, str(len(low_stock_products)))
                print(f"✅ Alertes de stock: {len(low_stock_products)}")

            # Dépenses du jour (si autorisé)
            if self.session_manager.has_permission('manage_expenses'):
                print("💸 Récupération des dépenses...")
                daily_expenses = self.expense_service.get_expenses_by_date(today)
                total_expenses = sum(expense.amount for expense in daily_expenses)
                if hasattr(self, 'daily_expenses_card'):
                    self.update_card_value(self.daily_expenses_card, f"{total_expenses:,.0f} {CURRENCY}")

                # Bénéfice estimé (ventes - dépenses)
                estimated_profit = daily_sales - total_expenses
                if hasattr(self, 'daily_profit_card'):
                    self.update_card_value(self.daily_profit_card, f"{estimated_profit:,.0f} {CURRENCY}")
                print(f"✅ Bénéfice estimé: {estimated_profit:,.0f} {CURRENCY}")
            else:
                # Si pas d'accès aux dépenses, afficher seulement les ventes
                if hasattr(self, 'daily_profit_card'):
                    self.update_card_value(self.daily_profit_card, f"{daily_sales:,.0f} {CURRENCY}")

            # Mettre à jour les alertes
            print("🚨 Mise à jour des alertes...")
            self.update_alerts(low_stock_products)

            # Mettre à jour les ventes récentes (si autorisé)
            if self.session_manager.has_permission('view_sales_history'):
                print("📊 Mise à jour des ventes récentes...")
                self.update_recent_sales(today)

            print("✅ Actualisation du tableau de bord terminée")

        except Exception as e:
            print(f"❌ Erreur lors de l'actualisation du tableau de bord: {e}")
            import traceback
            traceback.print_exc()
    
    def update_card_value(self, card, new_value):
        """Met à jour la valeur d'une carte"""
        value_label = card.findChild(QLabel, "card_value")
        if value_label:
            value_label.setText(new_value)
    
    def update_alerts(self, low_stock_products):
        """Met à jour les alertes"""
        try:
            # Récupérer toutes les alertes
            alerts = alert_manager.get_all_alerts()

            if alerts:
                alerts_text = ""
                # Afficher les 5 alertes les plus importantes
                for alert in alerts[:5]:
                    formatted_alert = alert_manager.format_alert_for_display(alert)
                    alerts_text += formatted_alert + "\n"

                if len(alerts) > 5:
                    alerts_text += f"\n... et {len(alerts) - 5} autre(s) alerte(s)"
            else:
                alerts_text = "✅ Aucune alerte pour le moment"

            self.alerts_label.setText(alerts_text)

        except Exception as e:
            print(f"Erreur lors de la mise à jour des alertes: {e}")
            # Fallback vers l'ancien système
            if low_stock_products:
                alerts_text = "⚠️ Produits en stock bas:\n"
                for product in low_stock_products[:5]:
                    alerts_text += f"• {product.name}: {product.current_stock} restant(s)\n"
                if len(low_stock_products) > 5:
                    alerts_text += f"... et {len(low_stock_products) - 5} autre(s)"
            else:
                alerts_text = "✅ Aucune alerte de stock pour le moment"

            self.alerts_label.setText(alerts_text)
    
    def update_recent_sales(self, today):
        """Met à jour le tableau des ventes récentes"""
        try:
            recent_sales = self.sale_service.get_sales_by_date(today)
            recent_sales = sorted(recent_sales, key=lambda x: x.created_at, reverse=True)[:10]
            
            self.recent_sales_table.setRowCount(len(recent_sales))
            
            for row, sale in enumerate(recent_sales):
                # Heure
                time_item = QTableWidgetItem(sale.created_at.strftime("%H:%M"))
                self.recent_sales_table.setItem(row, 0, time_item)
                
                # Table
                table_item = QTableWidgetItem(sale.table_number or "-")
                self.recent_sales_table.setItem(row, 1, table_item)
                
                # Client
                client_item = QTableWidgetItem(sale.customer_name or "-")
                self.recent_sales_table.setItem(row, 2, client_item)
                
                # Montant
                amount_item = QTableWidgetItem(f"{sale.total_amount:,.0f} {CURRENCY}")
                amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                self.recent_sales_table.setItem(row, 3, amount_item)
                
        except Exception as e:
            print(f"Erreur lors de la mise à jour des ventes récentes: {e}")
