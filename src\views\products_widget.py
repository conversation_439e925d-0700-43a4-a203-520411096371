#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Widget de gestion des produits - Bar-Resto Manager
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QPushButton, QTableWidget, QTableWidgetItem,
                            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox,
                            QTextEdit, QDialog, QDialogButtonBox, QMessageBox,
                            QHeaderView, QAbstractItemView, QGroupBox, QFormLayout)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QIcon

from models.database import DatabaseManager
from models.services import ProductService
from utils.auth import SessionManager
from utils.config import ProductCategory, CURRENCY

class ProductDialog(QDialog):
    """Dialog pour ajouter/modifier un produit"""

    def __init__(self, product=None, parent=None):
        super().__init__(parent)
        self.product = product
        self.product_service = ProductService(DatabaseManager())
        self.init_ui()

        if product:
            self.load_product_data()

    def init_ui(self):
        """Initialise l'interface du dialog"""
        self.setWindowTitle("Nouveau Produit" if not self.product else "Modifier Produit")
        self.setFixedSize(400, 500)

        layout = QVBoxLayout(self)

        # Formulaire
        form_layout = QFormLayout()

        # Nom du produit
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("Nom du produit")
        form_layout.addRow("Nom:", self.name_input)

        # Catégorie
        self.category_combo = QComboBox()
        self.category_combo.addItems([
            "Boisson", "Plat", "Snack"
        ])
        form_layout.addRow("Catégorie:", self.category_combo)

        # Prix d'achat
        self.purchase_price_input = QDoubleSpinBox()
        self.purchase_price_input.setRange(0, 999999)
        self.purchase_price_input.setSuffix(f" {CURRENCY}")
        form_layout.addRow("Prix d'achat:", self.purchase_price_input)

        # Prix de vente
        self.selling_price_input = QDoubleSpinBox()
        self.selling_price_input.setRange(0, 999999)
        self.selling_price_input.setSuffix(f" {CURRENCY}")
        form_layout.addRow("Prix de vente:", self.selling_price_input)

        # Stock initial
        self.initial_stock_input = QSpinBox()
        self.initial_stock_input.setRange(0, 99999)
        form_layout.addRow("Stock initial:", self.initial_stock_input)

        # Seuil d'alerte
        self.min_stock_input = QSpinBox()
        self.min_stock_input.setRange(0, 999)
        self.min_stock_input.setValue(5)
        form_layout.addRow("Seuil d'alerte:", self.min_stock_input)

        # Description
        self.description_input = QTextEdit()
        self.description_input.setMaximumHeight(80)
        self.description_input.setPlaceholderText("Description du produit (optionnel)")
        form_layout.addRow("Description:", self.description_input)

        layout.addLayout(form_layout)

        # Boutons
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        # Style
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox, QTextEdit {
                padding: 8px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background-color: white;
            }
            QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus, QTextEdit:focus {
                border-color: #007bff;
            }
        """)

    def load_product_data(self):
        """Charge les données du produit à modifier"""
        if self.product:
            self.name_input.setText(self.product.name)

            # Sélectionner la catégorie
            category_map = {"boisson": 0, "plat": 1, "snack": 2}
            self.category_combo.setCurrentIndex(category_map.get(self.product.category, 0))

            self.purchase_price_input.setValue(self.product.purchase_price)
            self.selling_price_input.setValue(self.product.selling_price)
            self.initial_stock_input.setValue(self.product.current_stock)
            self.min_stock_input.setValue(self.product.min_stock_alert)
            self.description_input.setPlainText(self.product.description or "")

    def get_product_data(self):
        """Retourne les données du formulaire"""
        category_map = {0: "boisson", 1: "plat", 2: "snack"}

        return {
            'name': self.name_input.text().strip(),
            'category': category_map[self.category_combo.currentIndex()],
            'purchase_price': self.purchase_price_input.value(),
            'selling_price': self.selling_price_input.value(),
            'initial_stock': self.initial_stock_input.value(),
            'min_stock_alert': self.min_stock_input.value(),
            'description': self.description_input.toPlainText().strip()
        }

    def accept(self):
        """Valide et sauvegarde le produit"""
        data = self.get_product_data()

        # Validation
        if not data['name']:
            QMessageBox.warning(self, "Erreur", "Le nom du produit est requis.")
            return

        if data['selling_price'] <= 0:
            QMessageBox.warning(self, "Erreur", "Le prix de vente doit être supérieur à 0.")
            return

        try:
            if self.product:
                # Modification
                self.product.name = data['name']
                self.product.category = data['category']
                self.product.purchase_price = data['purchase_price']
                self.product.selling_price = data['selling_price']
                self.product.min_stock_alert = data['min_stock_alert']
                self.product.description = data['description']
                # Note: Le stock n'est pas modifié ici, il faut passer par l'approvisionnement
            else:
                # Création
                self.product = self.product_service.create_product(
                    name=data['name'],
                    category=data['category'],
                    purchase_price=data['purchase_price'],
                    selling_price=data['selling_price'],
                    initial_stock=data['initial_stock'],
                    min_stock_alert=data['min_stock_alert'],
                    description=data['description']
                )

            super().accept()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la sauvegarde: {e}")

class ProductsWidget(QWidget):
    """Widget pour la gestion des produits"""

    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.product_service = ProductService(self.db_manager)
        self.session_manager = SessionManager()

        self.init_ui()
        self.setup_styles()
        self.refresh_data()

    def init_ui(self):
        """Initialise l'interface utilisateur"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)

        # Titre
        title_label = QLabel("🍽️ Gestion des Produits")
        title_label.setObjectName("page_title")
        layout.addWidget(title_label)

        # Barre d'outils
        self.create_toolbar(layout)

        # Filtres
        self.create_filters(layout)

        # Tableau des produits
        self.create_products_table(layout)

        # Statistiques
        self.create_stats_section(layout)

    def create_toolbar(self, layout):
        """Crée la barre d'outils"""
        toolbar_layout = QHBoxLayout()

        # Bouton Nouveau produit
        if self.session_manager.has_permission('manage_products'):
            self.new_button = QPushButton("➕ Nouveau Produit")
            self.new_button.setObjectName("primary_button")
            self.new_button.clicked.connect(self.add_product)
            toolbar_layout.addWidget(self.new_button)

            # Bouton Modifier
            self.edit_button = QPushButton("✏️ Modifier")
            self.edit_button.setObjectName("secondary_button")
            self.edit_button.clicked.connect(self.edit_product)
            self.edit_button.setEnabled(False)
            toolbar_layout.addWidget(self.edit_button)

            # Bouton Supprimer
            self.delete_button = QPushButton("🗑️ Supprimer")
            self.delete_button.setObjectName("danger_button")
            self.delete_button.clicked.connect(self.delete_product)
            self.delete_button.setEnabled(False)
            toolbar_layout.addWidget(self.delete_button)

        toolbar_layout.addStretch()

        # Bouton Actualiser
        refresh_button = QPushButton("🔄 Actualiser")
        refresh_button.setObjectName("secondary_button")
        refresh_button.clicked.connect(self.refresh_data)
        toolbar_layout.addWidget(refresh_button)

        layout.addLayout(toolbar_layout)

    def create_filters(self, layout):
        """Crée la section des filtres"""
        filters_group = QGroupBox("Filtres")
        filters_layout = QHBoxLayout(filters_group)

        # Filtre par catégorie
        filters_layout.addWidget(QLabel("Catégorie:"))
        self.category_filter = QComboBox()
        self.category_filter.addItems(["Toutes", "Boissons", "Plats", "Snacks"])
        self.category_filter.currentTextChanged.connect(self.apply_filters)
        filters_layout.addWidget(self.category_filter)

        # Filtre par stock
        filters_layout.addWidget(QLabel("Stock:"))
        self.stock_filter = QComboBox()
        self.stock_filter.addItems(["Tous", "Stock bas", "En stock", "Rupture"])
        self.stock_filter.currentTextChanged.connect(self.apply_filters)
        filters_layout.addWidget(self.stock_filter)

        # Recherche
        filters_layout.addWidget(QLabel("Recherche:"))
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Nom du produit...")
        self.search_input.textChanged.connect(self.apply_filters)
        filters_layout.addWidget(self.search_input)

        filters_layout.addStretch()

        layout.addWidget(filters_group)

    def create_products_table(self, layout):
        """Crée le tableau des produits"""
        self.products_table = QTableWidget()
        self.products_table.setColumnCount(8)
        self.products_table.setHorizontalHeaderLabels([
            "Nom", "Catégorie", "Prix Achat", "Prix Vente",
            "Stock", "Seuil", "Marge", "Statut"
        ])

        # Configuration du tableau
        header = self.products_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # Nom

        self.products_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.products_table.setAlternatingRowColors(True)
        self.products_table.setSortingEnabled(True)

        # Connexion des signaux
        self.products_table.selectionModel().selectionChanged.connect(self.on_selection_changed)
        self.products_table.doubleClicked.connect(self.edit_product)

        layout.addWidget(self.products_table)

    def create_stats_section(self, layout):
        """Crée la section des statistiques"""
        stats_group = QGroupBox("Statistiques")
        stats_layout = QGridLayout(stats_group)

        # Cartes de statistiques
        self.total_products_label = QLabel("0")
        self.total_products_label.setObjectName("stat_value")
        stats_layout.addWidget(QLabel("Total produits:"), 0, 0)
        stats_layout.addWidget(self.total_products_label, 0, 1)

        self.low_stock_label = QLabel("0")
        self.low_stock_label.setObjectName("stat_value_warning")
        stats_layout.addWidget(QLabel("Stock bas:"), 0, 2)
        stats_layout.addWidget(self.low_stock_label, 0, 3)

        self.total_value_label = QLabel(f"0 {CURRENCY}")
        self.total_value_label.setObjectName("stat_value")
        stats_layout.addWidget(QLabel("Valeur stock:"), 1, 0)
        stats_layout.addWidget(self.total_value_label, 1, 1)

        self.avg_margin_label = QLabel("0%")
        self.avg_margin_label.setObjectName("stat_value")
        stats_layout.addWidget(QLabel("Marge moyenne:"), 1, 2)
        stats_layout.addWidget(self.avg_margin_label, 1, 3)

        layout.addWidget(stats_group)

    def setup_styles(self):
        """Configure les styles CSS"""
        style = """
        #page_title {
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        #primary_button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: bold;
        }

        #primary_button:hover {
            background-color: #0056b3;
        }

        #secondary_button {
            background-color: #6c757d;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
        }

        #secondary_button:hover {
            background-color: #545b62;
        }

        #danger_button {
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
        }

        #danger_button:hover {
            background-color: #c82333;
        }

        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 5px;
            margin-top: 10px;
            padding-top: 10px;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }

        QTableWidget {
            gridline-color: #dee2e6;
            background-color: white;
            alternate-background-color: #f8f9fa;
        }

        QTableWidget::item {
            padding: 8px;
        }

        QHeaderView::section {
            background-color: #e9ecef;
            padding: 8px;
            border: none;
            font-weight: bold;
        }

        #stat_value {
            font-size: 18px;
            font-weight: bold;
            color: #28a745;
        }

        #stat_value_warning {
            font-size: 18px;
            font-weight: bold;
            color: #dc3545;
        }
        """
        self.setStyleSheet(style)

    def refresh_data(self):
        """Actualise les données du tableau"""
        try:
            # Récupérer tous les produits
            self.products = self.product_service.get_all_products()
            self.apply_filters()
            self.update_statistics()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des données: {e}")

    def apply_filters(self):
        """Applique les filtres au tableau"""
        if not hasattr(self, 'products'):
            return

        # Filtrer les produits
        filtered_products = self.products.copy()

        # Filtre par catégorie
        category_filter = self.category_filter.currentText()
        if category_filter != "Toutes":
            category_map = {"Boissons": "boisson", "Plats": "plat", "Snacks": "snack"}
            filtered_products = [p for p in filtered_products
                               if p.category == category_map.get(category_filter)]

        # Filtre par stock
        stock_filter = self.stock_filter.currentText()
        if stock_filter == "Stock bas":
            filtered_products = [p for p in filtered_products if p.is_low_stock()]
        elif stock_filter == "En stock":
            filtered_products = [p for p in filtered_products if p.current_stock > 0]
        elif stock_filter == "Rupture":
            filtered_products = [p for p in filtered_products if p.current_stock == 0]

        # Filtre par recherche
        search_text = self.search_input.text().lower()
        if search_text:
            filtered_products = [p for p in filtered_products
                               if search_text in p.name.lower()]

        # Mettre à jour le tableau
        self.populate_table(filtered_products)

    def populate_table(self, products):
        """Remplit le tableau avec les produits"""
        self.products_table.setRowCount(len(products))

        for row, product in enumerate(products):
            # Nom
            self.products_table.setItem(row, 0, QTableWidgetItem(product.name))

            # Catégorie
            category_display = {"boisson": "Boisson", "plat": "Plat", "snack": "Snack"}
            self.products_table.setItem(row, 1,
                QTableWidgetItem(category_display.get(product.category, product.category)))

            # Prix d'achat
            purchase_item = QTableWidgetItem(f"{product.purchase_price:,.0f} {CURRENCY}")
            purchase_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.products_table.setItem(row, 2, purchase_item)

            # Prix de vente
            selling_item = QTableWidgetItem(f"{product.selling_price:,.0f} {CURRENCY}")
            selling_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.products_table.setItem(row, 3, selling_item)

            # Stock
            stock_item = QTableWidgetItem(str(product.current_stock))
            stock_item.setTextAlignment(Qt.AlignCenter)
            if product.is_low_stock():
                stock_item.setBackground(Qt.red)
                stock_item.setForeground(Qt.white)
            self.products_table.setItem(row, 4, stock_item)

            # Seuil d'alerte
            threshold_item = QTableWidgetItem(str(product.min_stock_alert))
            threshold_item.setTextAlignment(Qt.AlignCenter)
            self.products_table.setItem(row, 5, threshold_item)

            # Marge
            if product.purchase_price > 0:
                margin = ((product.selling_price - product.purchase_price) / product.purchase_price) * 100
                margin_item = QTableWidgetItem(f"{margin:.1f}%")
            else:
                margin_item = QTableWidgetItem("N/A")
            margin_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.products_table.setItem(row, 6, margin_item)

            # Statut
            if product.current_stock == 0:
                status = "Rupture"
                status_color = Qt.red
            elif product.is_low_stock():
                status = "Stock bas"
                status_color = Qt.darkYellow
            else:
                status = "En stock"
                status_color = Qt.darkGreen

            status_item = QTableWidgetItem(status)
            status_item.setForeground(status_color)
            status_item.setTextAlignment(Qt.AlignCenter)
            self.products_table.setItem(row, 7, status_item)

            # Stocker l'objet produit dans la première cellule
            self.products_table.item(row, 0).setData(Qt.UserRole, product)

    def update_statistics(self):
        """Met à jour les statistiques"""
        if not hasattr(self, 'products'):
            return

        total_products = len(self.products)
        low_stock_count = len([p for p in self.products if p.is_low_stock()])

        # Valeur totale du stock
        total_value = sum(p.current_stock * p.purchase_price for p in self.products)

        # Marge moyenne
        margins = []
        for p in self.products:
            if p.purchase_price > 0:
                margin = ((p.selling_price - p.purchase_price) / p.purchase_price) * 100
                margins.append(margin)

        avg_margin = sum(margins) / len(margins) if margins else 0

        # Mettre à jour les labels
        self.total_products_label.setText(str(total_products))
        self.low_stock_label.setText(str(low_stock_count))
        self.total_value_label.setText(f"{total_value:,.0f} {CURRENCY}")
        self.avg_margin_label.setText(f"{avg_margin:.1f}%")

    def on_selection_changed(self):
        """Gère le changement de sélection dans le tableau"""
        has_selection = len(self.products_table.selectedItems()) > 0

        if hasattr(self, 'edit_button'):
            self.edit_button.setEnabled(has_selection)
        if hasattr(self, 'delete_button'):
            self.delete_button.setEnabled(has_selection)

    def get_selected_product(self):
        """Retourne le produit sélectionné"""
        current_row = self.products_table.currentRow()
        if current_row >= 0:
            item = self.products_table.item(current_row, 0)
            if item:
                return item.data(Qt.UserRole)
        return None

    def add_product(self):
        """Ajoute un nouveau produit"""
        dialog = ProductDialog(parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.refresh_data()
            QMessageBox.information(self, "Succès", "Produit ajouté avec succès!")

    def edit_product(self):
        """Modifie le produit sélectionné"""
        product = self.get_selected_product()
        if not product:
            QMessageBox.warning(self, "Aucune sélection", "Veuillez sélectionner un produit à modifier.")
            return

        dialog = ProductDialog(product=product, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.refresh_data()
            QMessageBox.information(self, "Succès", "Produit modifié avec succès!")

    def delete_product(self):
        """Supprime le produit sélectionné"""
        product = self.get_selected_product()
        if not product:
            QMessageBox.warning(self, "Aucune sélection", "Veuillez sélectionner un produit à supprimer.")
            return

        reply = QMessageBox.question(
            self, "Confirmer la suppression",
            f"Êtes-vous sûr de vouloir supprimer le produit '{product.name}' ?\n\n"
            "Cette action est irréversible.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # Marquer comme inactif plutôt que supprimer
                product.is_active = False
                self.refresh_data()
                QMessageBox.information(self, "Succès", "Produit supprimé avec succès!")

            except Exception as e:
                QMessageBox.critical(self, "Erreur", f"Erreur lors de la suppression: {e}")
