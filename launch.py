#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Lanceur simple pour Bar-Resto Manager
"""

import sys
import os

# Configuration du path
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

print("🚀 Démarrage de Bar-Resto Manager...")
print(f"📁 Répertoire courant: {current_dir}")
print(f"📁 Répertoire src: {src_dir}")

# Configuration Qt avant tout import
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt

if hasattr(Qt, 'AA_EnableHighDpiScaling'):
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

print("⚙️ Configuration Qt appliquée")

# Créer l'application Qt
app = QApplication(sys.argv)
app.setApplicationName("Bar-Resto Manager")
app.setApplicationVersion("1.0.0")

print("📱 Application Qt créée")

try:
    # Imports des modules
    print("📦 Import des modules...")
    from models.database import DatabaseManager
    from controllers.main_controller import MainController
    
    print("✅ Modules importés avec succès")
    
    # Initialiser la base de données
    print("🗄️ Initialisation de la base de données...")
    db_manager = DatabaseManager()
    db_manager.init_database()
    print("✅ Base de données prête")
    
    # Créer le contrôleur
    print("🎮 Création du contrôleur...")
    controller = MainController()
    print("✅ Contrôleur créé")
    
    # Afficher la fenêtre de connexion
    print("🔐 Affichage de la fenêtre de connexion...")
    controller.show_login()
    print("✅ Fenêtre de connexion affichée")
    
    print("\n" + "="*50)
    print("🎉 APPLICATION PRÊTE !")
    print("📋 Connexion par défaut:")
    print("   👤 Utilisateur: admin")
    print("   🔑 Mot de passe: admin123")
    print("="*50)
    
    # Lancer la boucle d'événements
    sys.exit(app.exec_())
    
except Exception as e:
    print(f"❌ ERREUR CRITIQUE: {e}")
    import traceback
    traceback.print_exc()
    
    # Afficher une fenêtre d'erreur
    from PyQt5.QtWidgets import QMessageBox
    msg = QMessageBox()
    msg.setIcon(QMessageBox.Critical)
    msg.setWindowTitle("Erreur de démarrage")
    msg.setText(f"Impossible de démarrer l'application:\n\n{str(e)}")
    msg.exec_()
    
    sys.exit(1)
