#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de test simple pour vérifier que l'application fonctionne
"""

import sys
import os

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """Test des imports principaux"""
    try:
        print("Test des imports...")
        
        # Test des utilitaires
        from utils.config import APP_NAME, UserRole, PERMISSIONS
        print(f"✓ Configuration chargée: {APP_NAME}")
        
        # Test de la base de données
        from models.database import DatabaseManager, User, Product
        print("✓ Modèles de base de données importés")
        
        # Test des services
        from models.services import UserService, ProductService
        print("✓ Services importés")
        
        # Test de l'authentification
        from utils.auth import SessionManager
        print("✓ Système d'authentification importé")
        
        print("\n✅ Tous les imports sont réussis !")
        return True
        
    except ImportError as e:
        print(f"❌ Erreur d'import: {e}")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_database():
    """Test de la base de données"""
    try:
        print("\nTest de la base de données...")
        
        from models.database import DatabaseManager
        
        # Initialiser la base de données
        db_manager = DatabaseManager()
        db_manager.init_database()
        print("✓ Base de données initialisée")
        
        # Test de session
        session = db_manager.get_session()
        session.close()
        print("✓ Session de base de données créée")
        
        print("✅ Base de données fonctionnelle !")
        return True
        
    except Exception as e:
        print(f"❌ Erreur de base de données: {e}")
        return False

def test_auth():
    """Test du système d'authentification"""
    try:
        print("\nTest du système d'authentification...")
        
        from utils.auth import SessionManager
        from models.services import UserService
        from models.database import DatabaseManager
        
        # Test du gestionnaire de session
        session_manager = SessionManager()
        print("✓ Gestionnaire de session créé")
        
        # Test de connexion avec l'admin par défaut
        if session_manager.login('admin', 'admin123'):
            print("✓ Connexion admin réussie")
            
            user = session_manager.get_current_user()
            print(f"✓ Utilisateur connecté: {user.full_name}")
            
            # Test des permissions
            if session_manager.has_permission('manage_users'):
                print("✓ Permissions admin vérifiées")
            
            session_manager.logout()
            print("✓ Déconnexion réussie")
        else:
            print("❌ Échec de la connexion admin")
            return False
        
        print("✅ Système d'authentification fonctionnel !")
        return True
        
    except Exception as e:
        print(f"❌ Erreur d'authentification: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🧪 Test de l'application Bar-Resto Manager")
    print("=" * 50)
    
    success = True
    
    # Test des imports
    if not test_imports():
        success = False
    
    # Test de la base de données
    if not test_database():
        success = False
    
    # Test de l'authentification
    if not test_auth():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Tous les tests sont réussis ! L'application est prête.")
        print("\nPour lancer l'application complète:")
        print("python main.py")
    else:
        print("❌ Certains tests ont échoué. Vérifiez les dépendances.")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
