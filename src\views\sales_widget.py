#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Widget de gestion des ventes - Bar-Resto Manager
"""

from datetime import datetime, date
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QPushButton, QTableWidget, QTableWidgetItem,
                            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox,
                            QTextEdit, QDialog, QDialogButtonBox, QMessageBox,
                            QHeaderView, QAbstractItemView, QGroupBox, QFormLayout,
                            QTabWidget, QSplitter, QListWidget, QListWidgetItem,
                            QFrame, QScrollArea)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QIcon, QPalette

from models.database import DatabaseManager
from models.services import ProductService, SaleService
from utils.auth import SessionManager, get_current_user
from utils.config import ProductCategory, CURRENCY

class SaleItem:
    """Classe pour représenter un article dans le panier"""
    def __init__(self, product, quantity=1):
        self.product = product
        self.quantity = quantity
        self.unit_price = product.selling_price
        self.total_price = self.unit_price * quantity

    def update_quantity(self, quantity):
        """Met à jour la quantité et recalcule le total"""
        self.quantity = quantity
        self.total_price = self.unit_price * quantity

class ProductCatalogWidget(QWidget):
    """Widget pour afficher le catalogue des produits"""

    product_selected = pyqtSignal(object)  # Signal émis quand un produit est sélectionné

    def __init__(self):
        super().__init__()
        self.product_service = ProductService(DatabaseManager())
        self.init_ui()
        self.load_products()

    def init_ui(self):
        """Initialise l'interface du catalogue"""
        layout = QVBoxLayout(self)

        # Filtres
        filters_layout = QHBoxLayout()

        # Filtre par catégorie
        filters_layout.addWidget(QLabel("Catégorie:"))
        self.category_filter = QComboBox()
        self.category_filter.addItems(["Toutes", "Boissons", "Plats", "Snacks"])
        self.category_filter.currentTextChanged.connect(self.filter_products)
        filters_layout.addWidget(self.category_filter)

        # Recherche
        filters_layout.addWidget(QLabel("Recherche:"))
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Nom du produit...")
        self.search_input.textChanged.connect(self.filter_products)
        filters_layout.addWidget(self.search_input)

        layout.addLayout(filters_layout)

        # Liste des produits
        self.products_list = QListWidget()
        self.products_list.itemDoubleClicked.connect(self.on_product_double_clicked)
        layout.addWidget(self.products_list)

    def load_products(self):
        """Charge les produits disponibles"""
        try:
            self.products = self.product_service.get_all_products()
            # Filtrer seulement les produits en stock
            self.products = [p for p in self.products if p.current_stock > 0]
            self.filter_products()
        except Exception as e:
            print(f"Erreur lors du chargement des produits: {e}")

    def filter_products(self):
        """Filtre et affiche les produits"""
        self.products_list.clear()

        if not hasattr(self, 'products'):
            return

        filtered_products = self.products.copy()

        # Filtre par catégorie
        category_filter = self.category_filter.currentText()
        if category_filter != "Toutes":
            category_map = {"Boissons": "boisson", "Plats": "plat", "Snacks": "snack"}
            filtered_products = [p for p in filtered_products
                               if p.category == category_map.get(category_filter)]

        # Filtre par recherche
        search_text = self.search_input.text().lower()
        if search_text:
            filtered_products = [p for p in filtered_products
                               if search_text in p.name.lower()]

        # Ajouter les produits à la liste
        for product in filtered_products:
            item = QListWidgetItem()
            item.setText(f"{product.name}\n{product.selling_price:,.0f} {CURRENCY} - Stock: {product.current_stock}")
            item.setData(Qt.UserRole, product)

            # Couleur selon le stock
            if product.is_low_stock():
                item.setBackground(Qt.yellow)

            self.products_list.addItem(item)

    def on_product_double_clicked(self, item):
        """Gère le double-clic sur un produit"""
        product = item.data(Qt.UserRole)
        if product:
            self.product_selected.emit(product)

class CartWidget(QWidget):
    """Widget pour afficher le panier"""

    total_changed = pyqtSignal(float)  # Signal émis quand le total change

    def __init__(self):
        super().__init__()
        self.cart_items = []  # Liste des SaleItem
        self.init_ui()

    def init_ui(self):
        """Initialise l'interface du panier"""
        layout = QVBoxLayout(self)

        # Titre
        title_label = QLabel("🛒 Panier")
        title_label.setObjectName("cart_title")
        layout.addWidget(title_label)

        # Tableau du panier
        self.cart_table = QTableWidget()
        self.cart_table.setColumnCount(5)
        self.cart_table.setHorizontalHeaderLabels([
            "Produit", "Prix Unit.", "Qté", "Total", "Action"
        ])

        # Configuration du tableau
        header = self.cart_table.horizontalHeader()
        header.setStretchLastSection(False)
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # Produit
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Prix
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Quantité
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Total
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Action

        self.cart_table.setAlternatingRowColors(True)
        layout.addWidget(self.cart_table)

        # Total
        total_frame = QFrame()
        total_frame.setFrameStyle(QFrame.Box)
        total_layout = QHBoxLayout(total_frame)

        total_layout.addWidget(QLabel("TOTAL:"))
        self.total_label = QLabel(f"0 {CURRENCY}")
        self.total_label.setObjectName("total_amount")
        total_layout.addWidget(self.total_label)

        layout.addWidget(total_frame)

        # Boutons d'action
        buttons_layout = QHBoxLayout()

        self.clear_button = QPushButton("🗑️ Vider")
        self.clear_button.setObjectName("danger_button")
        self.clear_button.clicked.connect(self.clear_cart)
        buttons_layout.addWidget(self.clear_button)

        buttons_layout.addStretch()

        self.checkout_button = QPushButton("💳 Finaliser")
        self.checkout_button.setObjectName("primary_button")
        self.checkout_button.setEnabled(False)
        buttons_layout.addWidget(self.checkout_button)

        layout.addLayout(buttons_layout)

    def add_product(self, product):
        """Ajoute un produit au panier"""
        # Vérifier si le produit est déjà dans le panier
        for item in self.cart_items:
            if item.product.id == product.id:
                # Augmenter la quantité si possible
                if item.quantity < product.current_stock:
                    item.update_quantity(item.quantity + 1)
                    self.refresh_cart()
                    return
                else:
                    QMessageBox.warning(self, "Stock insuffisant",
                                      f"Stock disponible: {product.current_stock}")
                    return

        # Ajouter un nouveau produit
        if product.current_stock > 0:
            sale_item = SaleItem(product)
            self.cart_items.append(sale_item)
            self.refresh_cart()
        else:
            QMessageBox.warning(self, "Rupture de stock",
                              f"Le produit '{product.name}' n'est plus en stock.")

    def refresh_cart(self):
        """Actualise l'affichage du panier"""
        self.cart_table.setRowCount(len(self.cart_items))

        total = 0
        for row, item in enumerate(self.cart_items):
            # Nom du produit
            self.cart_table.setItem(row, 0, QTableWidgetItem(item.product.name))

            # Prix unitaire
            price_item = QTableWidgetItem(f"{item.unit_price:,.0f} {CURRENCY}")
            price_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.cart_table.setItem(row, 1, price_item)

            # Quantité (modifiable)
            quantity_spin = QSpinBox()
            quantity_spin.setRange(1, item.product.current_stock)
            quantity_spin.setValue(item.quantity)
            quantity_spin.valueChanged.connect(lambda value, i=item: self.update_quantity(i, value))
            self.cart_table.setCellWidget(row, 2, quantity_spin)

            # Total
            total_item = QTableWidgetItem(f"{item.total_price:,.0f} {CURRENCY}")
            total_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.cart_table.setItem(row, 3, total_item)

            # Bouton supprimer
            remove_button = QPushButton("❌")
            remove_button.setMaximumWidth(30)
            remove_button.clicked.connect(lambda checked, i=item: self.remove_item(i))
            self.cart_table.setCellWidget(row, 4, remove_button)

            total += item.total_price

        # Mettre à jour le total
        self.total_label.setText(f"{total:,.0f} {CURRENCY}")
        self.checkout_button.setEnabled(len(self.cart_items) > 0)
        self.total_changed.emit(total)

    def update_quantity(self, item, quantity):
        """Met à jour la quantité d'un article"""
        item.update_quantity(quantity)
        self.refresh_cart()

    def remove_item(self, item):
        """Supprime un article du panier"""
        if item in self.cart_items:
            self.cart_items.remove(item)
            self.refresh_cart()

    def clear_cart(self):
        """Vide le panier"""
        if self.cart_items:
            reply = QMessageBox.question(
                self, "Vider le panier",
                "Êtes-vous sûr de vouloir vider le panier ?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.cart_items.clear()
                self.refresh_cart()

    def get_cart_data(self):
        """Retourne les données du panier pour la vente"""
        return [
            {
                'product_id': item.product.id,
                'quantity': item.quantity
            }
            for item in self.cart_items
        ]

    def get_total(self):
        """Retourne le total du panier"""
        return sum(item.total_price for item in self.cart_items)

class CheckoutDialog(QDialog):
    """Dialog pour finaliser une vente"""

    def __init__(self, cart_data, total_amount, parent=None):
        super().__init__(parent)
        self.cart_data = cart_data
        self.total_amount = total_amount
        self.sale_service = SaleService(DatabaseManager())
        self.init_ui()

    def init_ui(self):
        """Initialise l'interface du dialog"""
        self.setWindowTitle("Finaliser la vente")
        self.setFixedSize(400, 300)

        layout = QVBoxLayout(self)

        # Résumé de la commande
        summary_group = QGroupBox("Résumé de la commande")
        summary_layout = QFormLayout(summary_group)

        summary_layout.addRow("Nombre d'articles:", QLabel(str(len(self.cart_data))))
        summary_layout.addRow("Total:", QLabel(f"{self.total_amount:,.0f} {CURRENCY}"))

        layout.addWidget(summary_group)

        # Informations client
        client_group = QGroupBox("Informations client")
        client_layout = QFormLayout(client_group)

        self.table_input = QLineEdit()
        self.table_input.setPlaceholderText("Numéro de table (optionnel)")
        client_layout.addRow("Table:", self.table_input)

        self.customer_input = QLineEdit()
        self.customer_input.setPlaceholderText("Nom du client (optionnel)")
        client_layout.addRow("Client:", self.customer_input)

        layout.addWidget(client_group)

        # Mode de paiement
        payment_group = QGroupBox("Mode de paiement")
        payment_layout = QFormLayout(payment_group)

        self.payment_combo = QComboBox()
        self.payment_combo.addItems(["Espèces", "Carte", "Mobile Money"])
        payment_layout.addRow("Paiement:", self.payment_combo)

        layout.addWidget(payment_group)

        # Notes
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(60)
        self.notes_input.setPlaceholderText("Notes (optionnel)")
        layout.addWidget(QLabel("Notes:"))
        layout.addWidget(self.notes_input)

        # Boutons
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        button_box.accepted.connect(self.process_sale)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

    def process_sale(self):
        """Traite la vente"""
        try:
            current_user = get_current_user()
            if not current_user:
                QMessageBox.critical(self, "Erreur", "Utilisateur non connecté.")
                return

            # Mapper le mode de paiement
            payment_map = {"Espèces": "cash", "Carte": "card", "Mobile Money": "mobile"}
            payment_method = payment_map[self.payment_combo.currentText()]

            # Créer la vente
            sale = self.sale_service.create_sale(
                user_id=current_user.id,
                items=self.cart_data,
                table_number=self.table_input.text().strip(),
                customer_name=self.customer_input.text().strip(),
                payment_method=payment_method,
                notes=self.notes_input.toPlainText().strip()
            )

            if sale:
                QMessageBox.information(
                    self, "Vente réussie",
                    f"Vente #{sale.id} enregistrée avec succès!\n"
                    f"Total: {sale.total_amount:,.0f} {CURRENCY}"
                )
                self.accept()
            else:
                QMessageBox.critical(
                    self, "Erreur",
                    "Erreur lors de l'enregistrement de la vente.\n"
                    "Vérifiez les stocks disponibles."
                )

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du traitement: {e}")

class SalesWidget(QWidget):
    """Widget principal pour la gestion des ventes"""

    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.sale_service = SaleService(self.db_manager)
        self.session_manager = SessionManager()

        self.init_ui()
        self.setup_styles()
        self.refresh_data()

    def init_ui(self):
        """Initialise l'interface utilisateur"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)

        # Titre
        title_label = QLabel("🛒 Point de Vente")
        title_label.setObjectName("page_title")
        layout.addWidget(title_label)

        # Onglets
        self.tab_widget = QTabWidget()

        # Onglet Nouvelle vente
        self.create_new_sale_tab()

        # Onglet Historique (si autorisé)
        if self.session_manager.has_permission('view_sales_history'):
            self.create_history_tab()

        layout.addWidget(self.tab_widget)

    def create_new_sale_tab(self):
        """Crée l'onglet de nouvelle vente"""
        new_sale_widget = QWidget()
        layout = QHBoxLayout(new_sale_widget)

        # Splitter pour diviser l'écran
        splitter = QSplitter(Qt.Horizontal)

        # Catalogue des produits (gauche)
        self.catalog_widget = ProductCatalogWidget()
        self.catalog_widget.product_selected.connect(self.add_to_cart)
        splitter.addWidget(self.catalog_widget)

        # Panier (droite)
        self.cart_widget = CartWidget()
        self.cart_widget.checkout_button.clicked.connect(self.checkout)
        splitter.addWidget(self.cart_widget)

        # Proportions 60/40
        splitter.setSizes([600, 400])

        layout.addWidget(splitter)
        self.tab_widget.addTab(new_sale_widget, "Nouvelle Vente")

    def create_history_tab(self):
        """Crée l'onglet d'historique des ventes"""
        history_widget = QWidget()
        layout = QVBoxLayout(history_widget)

        # Filtres
        filters_layout = QHBoxLayout()

        filters_layout.addWidget(QLabel("Date:"))
        self.date_filter = QComboBox()
        self.date_filter.addItems([
            "Aujourd'hui", "Hier", "Cette semaine", "Ce mois"
        ])
        self.date_filter.currentTextChanged.connect(self.filter_sales_history)
        filters_layout.addWidget(self.date_filter)

        filters_layout.addStretch()

        refresh_history_button = QPushButton("🔄 Actualiser")
        refresh_history_button.clicked.connect(self.refresh_sales_history)
        filters_layout.addWidget(refresh_history_button)

        layout.addLayout(filters_layout)

        # Tableau des ventes
        self.sales_table = QTableWidget()
        self.sales_table.setColumnCount(6)
        self.sales_table.setHorizontalHeaderLabels([
            "ID", "Date/Heure", "Table", "Client", "Total", "Paiement"
        ])

        # Configuration du tableau
        header = self.sales_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # Date/Heure

        self.sales_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.sales_table.setAlternatingRowColors(True)
        self.sales_table.setSortingEnabled(True)

        layout.addWidget(self.sales_table)

        # Statistiques du jour
        stats_group = QGroupBox("Statistiques du jour")
        stats_layout = QGridLayout(stats_group)

        self.daily_sales_count_label = QLabel("0")
        self.daily_sales_count_label.setObjectName("stat_value")
        stats_layout.addWidget(QLabel("Nombre de ventes:"), 0, 0)
        stats_layout.addWidget(self.daily_sales_count_label, 0, 1)

        self.daily_total_label = QLabel(f"0 {CURRENCY}")
        self.daily_total_label.setObjectName("stat_value")
        stats_layout.addWidget(QLabel("Total du jour:"), 0, 2)
        stats_layout.addWidget(self.daily_total_label, 0, 3)

        layout.addWidget(stats_group)

        self.tab_widget.addTab(history_widget, "Historique")

    def setup_styles(self):
        """Configure les styles CSS"""
        style = """
        #page_title {
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        #cart_title {
            font-size: 18px;
            font-weight: bold;
            color: #495057;
            margin-bottom: 10px;
        }

        #total_amount {
            font-size: 24px;
            font-weight: bold;
            color: #28a745;
        }

        #primary_button {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            font-weight: bold;
            font-size: 14px;
        }

        #primary_button:hover {
            background-color: #218838;
        }

        #danger_button {
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
        }

        #danger_button:hover {
            background-color: #c82333;
        }

        #stat_value {
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
        }

        QListWidget {
            background-color: white;
            border: 1px solid #dee2e6;
            border-radius: 5px;
        }

        QListWidget::item {
            padding: 10px;
            border-bottom: 1px solid #f8f9fa;
        }

        QListWidget::item:hover {
            background-color: #e9ecef;
        }

        QListWidget::item:selected {
            background-color: #007bff;
            color: white;
        }

        QTableWidget {
            gridline-color: #dee2e6;
            background-color: white;
            alternate-background-color: #f8f9fa;
        }

        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 5px;
            margin-top: 10px;
            padding-top: 10px;
        }
        """
        self.setStyleSheet(style)

    def add_to_cart(self, product):
        """Ajoute un produit au panier"""
        self.cart_widget.add_product(product)

    def checkout(self):
        """Finalise la vente"""
        cart_data = self.cart_widget.get_cart_data()
        total = self.cart_widget.get_total()

        if not cart_data:
            QMessageBox.warning(self, "Panier vide", "Ajoutez des produits au panier avant de finaliser.")
            return

        dialog = CheckoutDialog(cart_data, total, self)
        if dialog.exec_() == QDialog.Accepted:
            # Vider le panier après une vente réussie
            self.cart_widget.clear_cart()
            # Actualiser le catalogue pour les stocks
            self.catalog_widget.load_products()
            # Actualiser l'historique si visible
            if hasattr(self, 'sales_table'):
                self.refresh_sales_history()

    def refresh_data(self):
        """Actualise les données"""
        if hasattr(self, 'catalog_widget'):
            self.catalog_widget.load_products()
        if hasattr(self, 'sales_table'):
            self.refresh_sales_history()

    def show_history(self):
        """Affiche l'onglet historique"""
        if hasattr(self, 'tab_widget') and self.tab_widget.count() > 1:
            self.tab_widget.setCurrentIndex(1)

    def refresh_sales_history(self):
        """Actualise l'historique des ventes"""
        if not hasattr(self, 'sales_table'):
            return

        try:
            # Récupérer les ventes du jour
            today = date.today()
            sales = self.sale_service.get_sales_by_date(today)

            # Trier par date décroissante
            sales.sort(key=lambda x: x.created_at, reverse=True)

            # Remplir le tableau
            self.sales_table.setRowCount(len(sales))

            total_amount = 0
            for row, sale in enumerate(sales):
                # ID
                self.sales_table.setItem(row, 0, QTableWidgetItem(str(sale.id)))

                # Date/Heure
                date_str = sale.created_at.strftime("%d/%m/%Y %H:%M")
                self.sales_table.setItem(row, 1, QTableWidgetItem(date_str))

                # Table
                table_item = QTableWidgetItem(sale.table_number or "-")
                table_item.setTextAlignment(Qt.AlignCenter)
                self.sales_table.setItem(row, 2, table_item)

                # Client
                self.sales_table.setItem(row, 3, QTableWidgetItem(sale.customer_name or "-"))

                # Total
                total_item = QTableWidgetItem(f"{sale.total_amount:,.0f} {CURRENCY}")
                total_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                self.sales_table.setItem(row, 4, total_item)

                # Mode de paiement
                payment_map = {"cash": "Espèces", "card": "Carte", "mobile": "Mobile"}
                payment_text = payment_map.get(sale.payment_method, sale.payment_method)
                self.sales_table.setItem(row, 5, QTableWidgetItem(payment_text))

                total_amount += sale.total_amount

            # Mettre à jour les statistiques
            self.daily_sales_count_label.setText(str(len(sales)))
            self.daily_total_label.setText(f"{total_amount:,.0f} {CURRENCY}")

        except Exception as e:
            print(f"Erreur lors de l'actualisation de l'historique: {e}")

    def filter_sales_history(self):
        """Filtre l'historique des ventes"""
        # Pour l'instant, on affiche seulement les ventes du jour
        # Cette méthode peut être étendue pour d'autres filtres
        self.refresh_sales_history()
