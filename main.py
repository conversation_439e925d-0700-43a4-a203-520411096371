#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Application de Gestion de Stock - Bar Restaurant
Point d'entrée principal de l'application
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from controllers.main_controller import MainController
from models.database import DatabaseManager

def main():
    """Point d'entrée principal de l'application"""
    app = QApplication(sys.argv)
    app.setApplicationName("Bar-Resto Manager")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("BarResto Solutions")
    
    # Configuration de l'application
    app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    # Initialiser la base de données
    db_manager = DatabaseManager()
    db_manager.init_database()
    
    # Créer et afficher le contrôleur principal
    controller = MainController()
    controller.show_login()
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
