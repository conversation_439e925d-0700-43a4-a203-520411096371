#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Système d'alertes pour l'application Bar-Resto Manager
"""

from datetime import datetime, date, timedelta
from typing import List, Dict, Any
from enum import Enum

from models.database import DatabaseManager
from models.services import ProductService, SaleService, ExpenseService
from utils.config import STOCK_ALERT_THRESHOLD, CURRENCY

class AlertType(Enum):
    """Types d'alertes"""
    STOCK_LOW = "stock_low"
    STOCK_OUT = "stock_out"
    HIGH_EXPENSES = "high_expenses"
    LOW_SALES = "low_sales"
    PRODUCT_INACTIVE = "product_inactive"

class AlertLevel(Enum):
    """Niveaux d'alerte"""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"

class Alert:
    """Classe représentant une alerte"""
    
    def __init__(self, alert_type: AlertType, level: AlertLevel, title: str, 
                 message: str, data: Dict[str, Any] = None):
        self.alert_type = alert_type
        self.level = level
        self.title = title
        self.message = message
        self.data = data or {}
        self.timestamp = datetime.now()
    
    def __str__(self):
        return f"[{self.level.value.upper()}] {self.title}: {self.message}"

class AlertManager:
    """Gestionnaire d'alertes"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.product_service = ProductService(self.db_manager)
        self.sale_service = SaleService(self.db_manager)
        self.expense_service = ExpenseService(self.db_manager)
    
    def get_all_alerts(self) -> List[Alert]:
        """Récupère toutes les alertes actives"""
        alerts = []
        
        # Alertes de stock
        alerts.extend(self.get_stock_alerts())
        
        # Alertes de ventes
        alerts.extend(self.get_sales_alerts())
        
        # Alertes de dépenses
        alerts.extend(self.get_expense_alerts())
        
        # Trier par niveau de criticité
        priority_order = {AlertLevel.CRITICAL: 0, AlertLevel.WARNING: 1, AlertLevel.INFO: 2}
        alerts.sort(key=lambda x: priority_order[x.level])
        
        return alerts
    
    def get_stock_alerts(self) -> List[Alert]:
        """Génère les alertes de stock"""
        alerts = []
        
        try:
            # Produits en stock bas
            low_stock_products = self.product_service.get_low_stock_products()
            
            for product in low_stock_products:
                if product.current_stock == 0:
                    # Rupture de stock
                    alert = Alert(
                        alert_type=AlertType.STOCK_OUT,
                        level=AlertLevel.CRITICAL,
                        title="Rupture de stock",
                        message=f"Le produit '{product.name}' n'est plus en stock",
                        data={"product_id": product.id, "product_name": product.name}
                    )
                    alerts.append(alert)
                else:
                    # Stock bas
                    alert = Alert(
                        alert_type=AlertType.STOCK_LOW,
                        level=AlertLevel.WARNING,
                        title="Stock bas",
                        message=f"Le produit '{product.name}' a un stock bas ({product.current_stock} restant)",
                        data={
                            "product_id": product.id, 
                            "product_name": product.name,
                            "current_stock": product.current_stock,
                            "threshold": product.min_stock_alert
                        }
                    )
                    alerts.append(alert)
        
        except Exception as e:
            print(f"Erreur lors de la génération des alertes de stock: {e}")
        
        return alerts
    
    def get_sales_alerts(self) -> List[Alert]:
        """Génère les alertes de ventes"""
        alerts = []
        
        try:
            today = date.today()
            yesterday = today - timedelta(days=1)
            
            # Ventes d'aujourd'hui vs hier
            today_sales = self.sale_service.get_daily_sales_total(today)
            yesterday_sales = self.sale_service.get_daily_sales_total(yesterday)
            
            # Si les ventes d'aujourd'hui sont significativement plus basses
            if yesterday_sales > 0 and today_sales < (yesterday_sales * 0.5):
                alert = Alert(
                    alert_type=AlertType.LOW_SALES,
                    level=AlertLevel.WARNING,
                    title="Ventes faibles",
                    message=f"Les ventes d'aujourd'hui ({today_sales:,.0f} {CURRENCY}) sont "
                           f"inférieures à hier ({yesterday_sales:,.0f} {CURRENCY})",
                    data={
                        "today_sales": today_sales,
                        "yesterday_sales": yesterday_sales,
                        "difference": yesterday_sales - today_sales
                    }
                )
                alerts.append(alert)
        
        except Exception as e:
            print(f"Erreur lors de la génération des alertes de ventes: {e}")
        
        return alerts
    
    def get_expense_alerts(self) -> List[Alert]:
        """Génère les alertes de dépenses"""
        alerts = []
        
        try:
            today = date.today()
            
            # Dépenses du jour
            today_expenses = self.expense_service.get_expenses_by_date(today)
            total_today = sum(expense.amount for expense in today_expenses)
            
            # Ventes du jour
            today_sales = self.sale_service.get_daily_sales_total(today)
            
            # Si les dépenses représentent plus de 80% des ventes
            if today_sales > 0 and total_today > (today_sales * 0.8):
                alert = Alert(
                    alert_type=AlertType.HIGH_EXPENSES,
                    level=AlertLevel.WARNING,
                    title="Dépenses élevées",
                    message=f"Les dépenses du jour ({total_today:,.0f} {CURRENCY}) "
                           f"représentent {(total_today/today_sales)*100:.1f}% des ventes",
                    data={
                        "expenses": total_today,
                        "sales": today_sales,
                        "percentage": (total_today/today_sales)*100
                    }
                )
                alerts.append(alert)
        
        except Exception as e:
            print(f"Erreur lors de la génération des alertes de dépenses: {e}")
        
        return alerts
    
    def get_critical_alerts_count(self) -> int:
        """Retourne le nombre d'alertes critiques"""
        alerts = self.get_all_alerts()
        return len([alert for alert in alerts if alert.level == AlertLevel.CRITICAL])
    
    def get_warning_alerts_count(self) -> int:
        """Retourne le nombre d'alertes d'avertissement"""
        alerts = self.get_all_alerts()
        return len([alert for alert in alerts if alert.level == AlertLevel.WARNING])
    
    def get_alerts_summary(self) -> Dict[str, int]:
        """Retourne un résumé des alertes"""
        alerts = self.get_all_alerts()
        
        summary = {
            "total": len(alerts),
            "critical": 0,
            "warning": 0,
            "info": 0
        }
        
        for alert in alerts:
            if alert.level == AlertLevel.CRITICAL:
                summary["critical"] += 1
            elif alert.level == AlertLevel.WARNING:
                summary["warning"] += 1
            elif alert.level == AlertLevel.INFO:
                summary["info"] += 1
        
        return summary
    
    def format_alert_for_display(self, alert: Alert) -> str:
        """Formate une alerte pour l'affichage"""
        level_icons = {
            AlertLevel.CRITICAL: "🔴",
            AlertLevel.WARNING: "🟡",
            AlertLevel.INFO: "🔵"
        }
        
        icon = level_icons.get(alert.level, "ℹ️")
        timestamp = alert.timestamp.strftime("%H:%M")
        
        return f"{icon} [{timestamp}] {alert.title}: {alert.message}"

# Instance globale du gestionnaire d'alertes
alert_manager = AlertManager()
